import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './components/auth/AuthContext';
import { AppStateProvider } from './contexts/AppStateContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginPage } from './components/auth/LoginPage';
import { Dashboard } from './components/dashboard/Dashboard';
import { ProjectView } from './components/projects/ProjectView';
import { FileView } from './components/files/FileView';
import { Layout } from './components/common/Layout';
import { NotificationSystem } from './components/common/NotificationSystem';
import './styles/App.css';

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppStateProvider>
          <div className="app">
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout>
                    <Dashboard />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/projects/:projectId" element={
                <ProtectedRoute>
                  <Layout>
                    <ProjectView />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/projects/:projectId/files" element={
                <ProtectedRoute>
                  <Layout>
                    <FileView />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            <NotificationSystem />
          </div>
        </AppStateProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
