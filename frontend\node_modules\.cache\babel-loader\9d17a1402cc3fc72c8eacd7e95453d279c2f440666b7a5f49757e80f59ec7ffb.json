{"ast": null, "code": "var _jsxFileName = \"D:\\\\GITHUB\\\\PDM\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './components/auth/AuthContext';\nimport { AppStateProvider } from './contexts/AppStateContext';\nimport { ProtectedRoute } from './components/auth/ProtectedRoute';\nimport { LoginPage } from './components/auth/LoginPage';\nimport { Dashboard } from './components/dashboard/Dashboard';\nimport { ProjectView } from './components/projects/ProjectView';\nimport { FileView } from './components/files/FileView';\nimport { Layout } from './components/common/Layout';\nimport { NotificationSystem } from './components/common/NotificationSystem';\nimport './styles/App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppStateProvider, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app\",\n        children: [/*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Layout, {\n                children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 25,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/projects/:projectId\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Layout, {\n                children: /*#__PURE__*/_jsxDEV(ProjectView, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/projects/:projectId/files\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Layout, {\n                children: /*#__PURE__*/_jsxDEV(FileView, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NotificationSystem, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "AppStateProvider", "ProtectedRoute", "LoginPage", "Dashboard", "ProjectView", "FileView", "Layout", "NotificationSystem", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/GITHUB/PDM/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './components/auth/AuthContext';\nimport { AppStateProvider } from './contexts/AppStateContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { ProtectedRoute } from './components/auth/ProtectedRoute';\nimport { LoginPage } from './components/auth/LoginPage';\nimport { Dashboard } from './components/dashboard/Dashboard';\nimport { ProjectView } from './components/projects/ProjectView';\nimport { FileView } from './components/files/FileView';\nimport { Layout } from './components/common/Layout';\nimport { NotificationSystem } from './components/common/NotificationSystem';\nimport './styles/App.css';\n\nconst App: React.FC = () => {\n  return (\n    <AuthProvider>\n      <AppStateProvider>\n        <div className=\"app\">\n          <Routes>\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"/\" element={\n              <ProtectedRoute>\n                <Layout>\n                  <Dashboard />\n                </Layout>\n              </ProtectedRoute>\n            } />\n            <Route path=\"/projects/:projectId\" element={\n              <ProtectedRoute>\n                <Layout>\n                  <ProjectView />\n                </Layout>\n              </ProtectedRoute>\n            } />\n            <Route path=\"/projects/:projectId/files\" element={\n              <ProtectedRoute>\n                <Layout>\n                  <FileView />\n                </Layout>\n              </ProtectedRoute>\n            } />\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n          <NotificationSystem />\n        </div>\n      </AppStateProvider>\n    </AuthProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,gBAAgB,QAAQ,4BAA4B;AAE7D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACV,YAAY;IAAAY,QAAA,eACXF,OAAA,CAACT,gBAAgB;MAAAW,QAAA,eACfF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBF,OAAA,CAACb,MAAM;UAAAe,QAAA,gBACLF,OAAA,CAACZ,KAAK;YAACgB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACP,SAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CT,OAAA,CAACZ,KAAK;YAACgB,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBL,OAAA,CAACR,cAAc;cAAAU,QAAA,eACbF,OAAA,CAACH,MAAM;gBAAAK,QAAA,eACLF,OAAA,CAACN,SAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJT,OAAA,CAACZ,KAAK;YAACgB,IAAI,EAAC,sBAAsB;YAACC,OAAO,eACxCL,OAAA,CAACR,cAAc;cAAAU,QAAA,eACbF,OAAA,CAACH,MAAM;gBAAAK,QAAA,eACLF,OAAA,CAACL,WAAW;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJT,OAAA,CAACZ,KAAK;YAACgB,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAC9CL,OAAA,CAACR,cAAc;cAAAU,QAAA,eACbF,OAAA,CAACH,MAAM;gBAAAK,QAAA,eACLF,OAAA,CAACJ,QAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJT,OAAA,CAACZ,KAAK;YAACgB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEL,OAAA,CAACX,QAAQ;cAACqB,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACTT,OAAA,CAACF,kBAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEnB,CAAC;AAACG,EAAA,GAnCIX,GAAa;AAqCnB,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}