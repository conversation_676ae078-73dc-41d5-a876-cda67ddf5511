import { Router, Request, Response } from 'express';
import { Pool } from 'pg';
import { ProjectManagementService } from '../services/ProjectManagementService';
import { auth } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../utils/logger';

export function createDashboardRoutes(db: Pool): Router {
  const router = Router();
  const projectService = new ProjectManagementService(db);

  /**
   * GET /api/dashboard/stats
   * Get dashboard statistics for the current user
   */
  router.get('/stats', auth, asyncHandler(async (req: Request, res: Response) => {
    const user = (req as any).user;
    
    logger.debug('Getting dashboard stats for user', { userId: user.id });
    
    try {
      // Get project stats
      const projectStats = await projectService.getProjectStats(user.id);
      
      // Get file count (we'll need to implement this or use a placeholder)
      // For now, let's use a placeholder until we implement file counting
      const totalFiles = 0; // TODO: Implement file counting across all user projects
      
      // Get recent activity count (placeholder for now)
      const recentActivity = 0; // TODO: Implement recent activity counting
      
      const dashboardStats = {
        totalProjects: projectStats.totalProjects,
        activeProjects: projectStats.activeProjects,
        totalFiles,
        recentActivity
      };

      logger.info('Dashboard stats retrieved successfully', { 
        userId: user.id, 
        stats: dashboardStats 
      });

      res.json({
        success: true,
        data: dashboardStats
      });
    } catch (error: any) {
      logger.error('Failed to get dashboard stats', { 
        error: error.message, 
        userId: user.id 
      });
      
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve dashboard statistics'
      });
    }
  }));

  return router;
}
