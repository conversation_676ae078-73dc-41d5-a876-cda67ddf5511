# PostgreSQL Remote Access Setup Guide

## For S-PI-ENGSVR PostgreSQL Configuration

### 1. Find PostgreSQL Data Directory
```cmd
# Windows - Run as Administrator
dir "C:\Program Files\PostgreSQL" /s /b | findstr postgresql.conf
```

### 2. Edit postgresql.conf
```ini
# Find and uncomment/change this line:
listen_addresses = '*'          # Listen on all interfaces
port = 5432                     # Default port
max_connections = 100           # Adjust as needed
```

### 3. Edit pg_hba.conf
Add these lines at the end:
```
# Allow connections from development machines
host    all             all             ***********/32         md5
host    all             all             *********/24           md5

# For specific database and user (more secure)
host    e3_pdm_system   postgres        ***********/32         md5
```

### 4. Restart PostgreSQL Service
```cmd
# Windows (as Administrator)
net stop postgresql-x64-14
net start postgresql-x64-14

# Or use Services.msc GUI
```

### 5. Configure Firewall
```powershell
# PowerShell (as Administrator)
New-NetFirewallRule -DisplayName "PostgreSQL-5432" -Direction Inbound -Protocol TCP -LocalPort 5432 -Action Allow
```

### 6. Create Database and User (if needed)
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE e3_pdm_system;
CREATE USER e3_pdm_user WITH PASSWORD '93JCpi123!$';
GRANT ALL PRIVILEGES ON DATABASE e3_pdm_system TO e3_pdm_user;
```

### 7. Test Connection
From development machine:
```bash
# Test basic connectivity
Test-NetConnection -ComputerName S-PI-ENGSVR -Port 5432

# Test database connection
node scripts/test-connection.js
```

## Troubleshooting

### Connection Still Fails?
1. Check if PostgreSQL service is running
2. Verify firewall rules
3. Check antivirus software blocking connections
4. Try connecting from the server itself first:
   ```cmd
   psql -h localhost -U postgres -d e3_pdm_system
   ```

### Security Considerations
- Use specific IP addresses instead of subnets when possible
- Consider using SSL connections in production
- Create dedicated database users instead of using 'postgres'
- Regularly update PostgreSQL for security patches

### Alternative: SSH Tunnel (if direct connection not possible)
```bash
# Create SSH tunnel (if SSH access available)
ssh -L 5432:localhost:5432 user@S-PI-ENGSVR
# Then connect to localhost:5432 from your app
```