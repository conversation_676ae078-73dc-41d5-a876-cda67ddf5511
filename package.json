{"name": "e3-pdm-system", "version": "1.0.0", "description": "Product Data Management system for e3 series design files", "main": "dist/index.js", "scripts": {"build": "tsc && npm run build:frontend", "build:backend": "tsc", "build:frontend": "cd frontend && npm run build", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "dev:frontend": "cd frontend && npm start", "test": "jest", "test:watch": "jest --watch", "test:frontend": "cd frontend && npm test", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "lint:frontend": "cd frontend && npm run lint", "db:init": "ts-node src/database/init.ts", "db:migrate": "ts-node scripts/migrate.ts", "db:rollback": "ts-node -e \"import('./src/database/migrations').then(m => m.createMigrationManager(require('./src/database/connection').getDatabase()).rollback())\"", "db:status": "ts-node -e \"import('./src/database/migrations').then(m => m.createMigrationManager(require('./src/database/connection').getDatabase()).getStatus().then(s => console.log(s)))\""}, "keywords": ["pdm", "e3series", "electrical-design", "file-management"], "author": "Development Team", "license": "MIT", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.1", "dxf-parser": "^1.1.2", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdfjs-dist": "^5.3.93", "pg": "^8.11.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}