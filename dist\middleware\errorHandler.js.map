{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;;;;AACA,4CAA8E;AAC9E,6DAAqC;AAM9B,MAAM,YAAY,GAAG,CAC1B,KAAuB,EACvB,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IAER,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,QAAkB,CAAC;IAGvB,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;QAC9B,QAAQ,GAAG,KAAK,CAAC;IACnB,CAAC;SAAM,CAAC;QAEN,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,gBAAgB,EAC1B,KAAK,CAAC,OAAO,EACb,GAAG,EACH,6BAA6B,CAC9B,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACtC,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,aAAa,EACvB,mBAAmB,EACnB,GAAG,EACH,8BAA8B,CAC/B,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAC9C,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,aAAa,EACvB,eAAe,EACf,GAAG,EACH,iCAAiC,CAClC,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAC9C,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,aAAa,EACvB,eAAe,EACf,GAAG,EACH,kCAAkC,CACnC,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,cAAc,EACxB,gBAAgB,EAChB,GAAG,EACH,uCAAuC,CACxC,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5F,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,0BAA0B,EACpC,4BAA4B,EAC5B,GAAG,EACH,mCAAmC,CACpC,CAAC;QACJ,CAAC;aAAM,CAAC;YAEN,QAAQ,GAAG,IAAI,iBAAQ,CACrB,kBAAS,CAAC,qBAAqB,EAC/B,KAAK,CAAC,OAAO,IAAI,uBAAuB,EACxC,GAAG,EACH,8BAA8B,EAC9B,SAAS,EACT,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAGD,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;IAC/D,MAAM,UAAU,GAAG;QACjB,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,aAAa,EAAE,QAAQ,CAAC,aAAa;YACrC,OAAO,EAAE,QAAQ,CAAC,OAAO;SAC1B;QACD,OAAO,EAAE;YACP,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;SAC9B;KACF,CAAC;IAEF,gBAAM,CAAC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAG/C,MAAM,aAAa,GAAQ;QACzB,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,OAAO,EAAE,QAAQ,CAAC,WAAW;YAC7B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,IAAI;gBAC/C,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,eAAe,EAAE,QAAQ,CAAC,OAAO;aAClC,CAAC;SACH;KACF,CAAC;IAGF,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;QAC9B,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,IAAA,+BAAsB,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1E,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,CAAC,CAAC;AAnHW,QAAA,YAAY,gBAmHvB;AAMK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAKK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAQ,EAAE;IACxF,MAAM,KAAK,GAAG,IAAI,iBAAQ,CACxB,kBAAS,CAAC,iBAAiB,EAC3B,SAAS,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,YAAY,EAClD,GAAG,EACH,sCAAsC,CACvC,CAAC;IACF,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AARW,QAAA,eAAe,mBAQ1B;AAKK,MAAM,sBAAsB,GAAG,CAAC,MAAa,EAAE,EAAE;IACtD,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;QAC3C,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;QAC7C,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,gBAAgB,EAC1B,mBAAmB,EACnB,GAAG,EACH,8BAA8B,EAC9B,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAC9B,CAAC;AACJ,CAAC,CAAC;AAbW,QAAA,sBAAsB,0BAajC;AAKK,MAAM,mBAAmB,GAAG,CAAC,KAAU,EAAS,EAAE;IACvD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,6BAA6B,EACvC,iBAAiB,EACjB,GAAG,EACH,+CAA+C,CAChD,CAAC;IACJ,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAClC,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,6BAA6B,EACvC,6BAA6B,EAC7B,GAAG,EACH,mDAAmD,CACpD,CAAC;IACJ,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAClC,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,gBAAgB,EAC1B,wBAAwB,EACxB,GAAG,EACH,iCAAiC,CAClC,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,qBAAqB,EAC/B,KAAK,CAAC,OAAO,IAAI,2BAA2B,EAC5C,GAAG,EACH,2BAA2B,CAC5B,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AA9BW,QAAA,mBAAmB,uBA8B9B;AAKK,MAAM,eAAe,GAAG,CAAC,KAAU,EAAE,QAAiB,EAAS,EAAE;IACtE,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,kBAAkB,EAC5B,qBAAqB,EACrB,GAAG,EACH,oDAAoD,CACrD,CAAC;IACJ,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;QAClD,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,mBAAmB,EAC7B,uBAAuB,EACvB,GAAG,EACH,4BAA4B,CAC7B,CAAC;IACJ,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7C,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,cAAc,EACxB,gBAAgB,EAChB,GAAG,EACH,uCAAuC,CACxC,CAAC;IACJ,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACtF,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,cAAc,EACxB,GAAG,QAAQ,IAAI,MAAM,0BAA0B,EAC/C,GAAG,EACH,6CAA6C,CAC9C,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,iBAAQ,CAChB,kBAAS,CAAC,sBAAsB,EAChC,KAAK,CAAC,OAAO,IAAI,wBAAwB,EACzC,GAAG,EACH,qCAAqC,CACtC,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AArCW,QAAA,eAAe,mBAqC1B"}