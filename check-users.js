const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD
});

async function checkUsers() {
  try {
    const result = await pool.query('SELECT username, email, role FROM users');
    console.log('Users in database:');
    if (result.rows.length === 0) {
      console.log('No users found in database');
    } else {
      result.rows.forEach(user => {
        console.log(`- ${user.username} (${user.email}) - ${user.role}`);
      });
    }
  } catch (err) {
    console.error('Error:', err.message);
  } finally {
    await pool.end();
  }
}

checkUsers();