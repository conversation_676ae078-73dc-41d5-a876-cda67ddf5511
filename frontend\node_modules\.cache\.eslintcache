[{"D:\\GITHUB\\PDM\\frontend\\src\\index.tsx": "1", "D:\\GITHUB\\PDM\\frontend\\src\\App.tsx": "2", "D:\\GITHUB\\PDM\\frontend\\src\\contexts\\AppStateContext.tsx": "3", "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "4", "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\AuthContext.tsx": "5", "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\LoginPage.tsx": "6", "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\NotificationSystem.tsx": "7", "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\Layout.tsx": "8", "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectView.tsx": "9", "D:\\GITHUB\\PDM\\frontend\\src\\components\\dashboard\\Dashboard.tsx": "10", "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileView.tsx": "11", "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectCard.tsx": "12", "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileList.tsx": "13", "D:\\GITHUB\\PDM\\frontend\\src\\services\\authService.ts": "14", "D:\\GITHUB\\PDM\\frontend\\src\\services\\projectService.ts": "15", "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\CreateProjectModal.tsx": "16", "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileUpload.tsx": "17", "D:\\GITHUB\\PDM\\frontend\\src\\services\\apiClient.ts": "18", "D:\\GITHUB\\PDM\\frontend\\src\\contexts\\ThemeContext.tsx": "19", "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\ThemeToggle.tsx": "20"}, {"size": 473, "mtime": 1753418140683, "results": "21", "hashOfConfig": "22"}, {"size": 1893, "mtime": 1753419669166, "results": "23", "hashOfConfig": "22"}, {"size": 9315, "mtime": 1753410243131, "results": "24", "hashOfConfig": "22"}, {"size": 713, "mtime": 1753409365604, "results": "25", "hashOfConfig": "22"}, {"size": 3892, "mtime": 1753409356035, "results": "26", "hashOfConfig": "22"}, {"size": 3624, "mtime": 1753413778584, "results": "27", "hashOfConfig": "22"}, {"size": 2054, "mtime": 1753410330777, "results": "28", "hashOfConfig": "22"}, {"size": 2651, "mtime": 1753419699281, "results": "29", "hashOfConfig": "22"}, {"size": 4672, "mtime": 1753409728005, "results": "30", "hashOfConfig": "22"}, {"size": 5052, "mtime": 1753419447694, "results": "31", "hashOfConfig": "22"}, {"size": 3678, "mtime": 1753409747466, "results": "32", "hashOfConfig": "22"}, {"size": 2070, "mtime": 1753409641132, "results": "33", "hashOfConfig": "22"}, {"size": 7691, "mtime": 1753409819090, "results": "34", "hashOfConfig": "22"}, {"size": 2779, "mtime": 1753418290307, "results": "35", "hashOfConfig": "22"}, {"size": 3757, "mtime": 1753410180007, "results": "36", "hashOfConfig": "22"}, {"size": 6565, "mtime": 1753409665387, "results": "37", "hashOfConfig": "22"}, {"size": 9658, "mtime": 1753409785924, "results": "38", "hashOfConfig": "22"}, {"size": 7043, "mtime": 1753410108415, "results": "39", "hashOfConfig": "22"}, {"size": 2190, "mtime": 1753419621055, "results": "40", "hashOfConfig": "22"}, {"size": 1332, "mtime": 1753419631049, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kd99dg", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\GITHUB\\PDM\\frontend\\src\\index.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\App.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\contexts\\AppStateContext.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\AuthContext.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\LoginPage.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\Layout.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectView.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\dashboard\\Dashboard.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileView.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectCard.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileList.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\services\\authService.ts", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\services\\projectService.ts", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\CreateProjectModal.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileUpload.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\services\\apiClient.ts", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\ThemeToggle.tsx", [], []]