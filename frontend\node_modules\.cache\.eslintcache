[{"D:\\GITHUB\\PDM\\frontend\\src\\index.tsx": "1", "D:\\GITHUB\\PDM\\frontend\\src\\App.tsx": "2", "D:\\GITHUB\\PDM\\frontend\\src\\contexts\\AppStateContext.tsx": "3", "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "4", "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\AuthContext.tsx": "5", "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\LoginPage.tsx": "6", "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\NotificationSystem.tsx": "7", "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\Layout.tsx": "8", "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectView.tsx": "9", "D:\\GITHUB\\PDM\\frontend\\src\\components\\dashboard\\Dashboard.tsx": "10", "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileView.tsx": "11", "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectCard.tsx": "12", "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileList.tsx": "13", "D:\\GITHUB\\PDM\\frontend\\src\\services\\authService.ts": "14", "D:\\GITHUB\\PDM\\frontend\\src\\services\\projectService.ts": "15", "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\CreateProjectModal.tsx": "16", "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileUpload.tsx": "17", "D:\\GITHUB\\PDM\\frontend\\src\\services\\apiClient.ts": "18"}, {"size": 473, "mtime": 1753418140683, "results": "19", "hashOfConfig": "20"}, {"size": 1731, "mtime": 1753410413913, "results": "21", "hashOfConfig": "20"}, {"size": 9315, "mtime": 1753410243131, "results": "22", "hashOfConfig": "20"}, {"size": 713, "mtime": 1753409365604, "results": "23", "hashOfConfig": "20"}, {"size": 3892, "mtime": 1753409356035, "results": "24", "hashOfConfig": "20"}, {"size": 3624, "mtime": 1753413778584, "results": "25", "hashOfConfig": "20"}, {"size": 2054, "mtime": 1753410330777, "results": "26", "hashOfConfig": "20"}, {"size": 2431, "mtime": 1753409607796, "results": "27", "hashOfConfig": "20"}, {"size": 4672, "mtime": 1753409728005, "results": "28", "hashOfConfig": "20"}, {"size": 5039, "mtime": 1753409628705, "results": "29", "hashOfConfig": "20"}, {"size": 3678, "mtime": 1753409747466, "results": "30", "hashOfConfig": "20"}, {"size": 2070, "mtime": 1753409641132, "results": "31", "hashOfConfig": "20"}, {"size": 7691, "mtime": 1753409819090, "results": "32", "hashOfConfig": "20"}, {"size": 2779, "mtime": 1753418290307, "results": "33", "hashOfConfig": "20"}, {"size": 3757, "mtime": 1753410180007, "results": "34", "hashOfConfig": "20"}, {"size": 6565, "mtime": 1753409665387, "results": "35", "hashOfConfig": "20"}, {"size": 9658, "mtime": 1753409785924, "results": "36", "hashOfConfig": "20"}, {"size": 7043, "mtime": 1753410108415, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kd99dg", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\GITHUB\\PDM\\frontend\\src\\index.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\App.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\contexts\\AppStateContext.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\AuthContext.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\auth\\LoginPage.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\common\\Layout.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectView.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\dashboard\\Dashboard.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileView.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\ProjectCard.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileList.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\services\\authService.ts", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\services\\projectService.ts", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\projects\\CreateProjectModal.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\components\\files\\FileUpload.tsx", [], [], "D:\\GITHUB\\PDM\\frontend\\src\\services\\apiClient.ts", [], []]