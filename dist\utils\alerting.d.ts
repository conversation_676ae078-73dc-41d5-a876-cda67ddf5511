import { AppError, ErrorCode } from './errors';
export interface AlertConfig {
    enabled: boolean;
    criticalErrorCodes: ErrorCode[];
    alertThreshold: number;
    timeWindow: number;
    emailRecipients: string[] | undefined;
    webhookUrl: string | undefined;
    slackWebhookUrl: string | undefined;
}
export declare class AlertManager {
    private config;
    private alertCooldowns;
    constructor(config?: Partial<AlertConfig>);
    processError(error: AppError, context?: any): Promise<void>;
    private shouldTrackError;
    private trackError;
    private shouldTriggerAlert;
    private triggerAlert;
    private sendWebhookAlert;
    private sendSlackAlert;
    private sendEmailAlert;
    getErrorStatistics(): Record<string, any>;
    clearErrorTracking(errorCode?: ErrorCode): void;
}
export declare const alertManager: AlertManager;
export declare const processErrorWithAlerting: (error: AppError, context?: any) => Promise<void>;
//# sourceMappingURL=alerting.d.ts.map