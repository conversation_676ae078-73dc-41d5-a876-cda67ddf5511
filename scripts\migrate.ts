#!/usr/bin/env node

/**
 * Database migration script
 */

import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import { getDatabase } from '../src/database/connection';
import { createMigrationManager } from '../src/database/migrations';

async function runMigrations() {
  try {
    console.log('🚀 Starting database migrations...');
    
    const db = getDatabase();
    const migrationManager = createMigrationManager(db);
    
    await migrationManager.migrate();
    
    console.log('✅ Migrations completed successfully');
    await db.close();
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigrations();