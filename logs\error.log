{
  error: {
    code: 'PROJECT_CREATION_FAILED',
    message: "Failed to create project: Failed to create project: Cannot read properties of undefined (reading 'logChange')",
    statusCode: 500,
    stack: "Error: Failed to create project: Failed to create project: Cannot read properties of undefined (reading 'logChange')\n" +
      '    at D:\\GITHUB\\PDM\\src\\routes\\projectRoutes.ts:123:24\n' +
      '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
    isOperational: true,
    details: undefined
  },
  request: {
    method: 'POST',
    url: '/api/projects',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9'
  },
  level: 'error',
  message: 'Error occurred',
  timestamp: '2025-07-24 23:58:17:5817'
}
{
  error: {
    code: 'PROJECT_CREATION_FAILED',
    message: "Failed to create project: Failed to create project: Cannot read properties of undefined (reading 'logChange')",
    statusCode: 500,
    stack: "Error: Failed to create project: Failed to create project: Cannot read properties of undefined (reading 'logChange')\n" +
      '    at D:\\GITHUB\\PDM\\src\\routes\\projectRoutes.ts:123:24\n' +
      '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
    isOperational: true,
    details: undefined
  },
  request: {
    method: 'POST',
    url: '/api/projects',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9'
  },
  level: 'error',
  message: 'Error occurred',
  timestamp: '2025-07-24 23:58:58:5858'
}
