#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
const dotenv = __importStar(require("dotenv"));
dotenv.config();
const connection_1 = require("./connection");
const migrations_1 = require("./migrations");
async function initializeDatabase() {
    console.log('🚀 Starting database initialization...');
    try {
        const db = connection_1.DatabaseConnection.getInstance(connection_1.dbConfig);
        console.log('📡 Testing database connection...');
        const isConnected = await db.testConnection();
        if (!isConnected) {
            throw new Error('Failed to connect to database');
        }
        console.log('✅ Database connection successful');
        const migrationManager = (0, migrations_1.createMigrationManager)(db);
        console.log('📋 Checking migration status...');
        const status = await migrationManager.getStatus();
        console.log(`Applied migrations: ${status.applied.length}`);
        console.log(`Pending migrations: ${status.pending.length}`);
        if (status.pending.length > 0) {
            console.log('🔄 Running pending migrations...');
            await migrationManager.migrate();
            console.log('✅ All migrations applied successfully');
        }
        else {
            console.log('✅ Database is up to date');
        }
        const finalStatus = await migrationManager.getStatus();
        console.log('\n📊 Database Status:');
        console.log(`- Total applied migrations: ${finalStatus.applied.length}`);
        console.log(`- Pending migrations: ${finalStatus.pending.length}`);
        if (finalStatus.applied.length > 0) {
            console.log('\n📝 Applied migrations:');
            finalStatus.applied.forEach(migration => {
                console.log(`  - ${migration.id}: ${migration.name} (${migration.appliedAt?.toISOString()})`);
            });
        }
        await db.close();
        console.log('\n🎉 Database initialization completed successfully!');
    }
    catch (error) {
        console.error('❌ Database initialization failed:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    initializeDatabase();
}
//# sourceMappingURL=init.js.map