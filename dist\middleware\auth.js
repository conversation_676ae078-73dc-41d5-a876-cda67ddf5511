"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = exports.AuthMiddleware = void 0;
exports.createAuthMiddleware = createAuthMiddleware;
const AuthenticationService_1 = require("../services/AuthenticationService");
class AuthMiddleware {
    constructor(authService, authzService) {
        this.authService = authService;
        this.authzService = authzService;
    }
    authenticate() {
        return async (req, res, next) => {
            try {
                const authHeader = req.headers.authorization;
                if (!authHeader || !authHeader.startsWith('Bearer ')) {
                    return res.status(401).json({
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'Missing or invalid authorization header',
                            timestamp: new Date().toISOString()
                        }
                    });
                }
                const token = authHeader.substring(7);
                try {
                    const user = await this.authService.validateToken(token);
                    req.user = user;
                    return next();
                }
                catch (error) {
                    return res.status(401).json({
                        error: {
                            code: 'INVALID_TOKEN',
                            message: 'Invalid or expired token',
                            timestamp: new Date().toISOString()
                        }
                    });
                }
            }
            catch (error) {
                return res.status(500).json({
                    error: {
                        code: 'INTERNAL_ERROR',
                        message: 'Authentication error',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        };
    }
    authorize(options = {}) {
        return async (req, res, next) => {
            try {
                if (!req.user) {
                    return res.status(401).json({
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                            timestamp: new Date().toISOString()
                        }
                    });
                }
                const { resource, permission, resourceIdParam } = options;
                if (!resource || !permission) {
                    return next();
                }
                let resourceId;
                if (resourceIdParam) {
                    resourceId = req.params[resourceIdParam] || req.body[resourceIdParam] || req.query[resourceIdParam];
                }
                const hasPermission = await this.authzService.hasPermission(req.user, resource, permission, resourceId);
                if (!hasPermission) {
                    return res.status(403).json({
                        error: {
                            code: 'FORBIDDEN',
                            message: `Insufficient permissions for ${permission} access to ${resource}`,
                            timestamp: new Date().toISOString()
                        }
                    });
                }
                return next();
            }
            catch (error) {
                return res.status(500).json({
                    error: {
                        code: 'INTERNAL_ERROR',
                        message: 'Authorization error',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        };
    }
    requireAdmin() {
        return this.authorize({
            resource: 'system',
            permission: 'admin'
        });
    }
    requireProjectAccess(permission = 'read') {
        return this.authorize({
            resource: 'project',
            permission,
            resourceIdParam: 'projectId'
        });
    }
    requireFileAccess(permission = 'read') {
        return this.authorize({
            resource: 'file',
            permission,
            resourceIdParam: 'fileId'
        });
    }
    requireSelfOrAdmin() {
        return async (req, res, next) => {
            try {
                if (!req.user) {
                    return res.status(401).json({
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                            timestamp: new Date().toISOString()
                        }
                    });
                }
                const targetUserId = req.params.userId || req.body.userId;
                if (req.user.id === targetUserId || req.user.role === 'admin') {
                    return next();
                }
                return res.status(403).json({
                    error: {
                        code: 'FORBIDDEN',
                        message: 'Can only access your own data unless you are an admin',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            catch (error) {
                return res.status(500).json({
                    error: {
                        code: 'INTERNAL_ERROR',
                        message: 'Authorization error',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        };
    }
}
exports.AuthMiddleware = AuthMiddleware;
function createAuthMiddleware(authService, authzService) {
    return new AuthMiddleware(authService, authzService);
}
const auth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Missing or invalid authorization header',
                    timestamp: new Date().toISOString()
                }
            });
        }
        const token = authHeader.substring(7);
        const { Pool } = require('pg');
        const dbConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '5432'),
            database: process.env.DB_NAME || 'e3_pdm_system',
            user: process.env.DB_USER || 'postgres',
            password: process.env.DB_PASSWORD || 'password',
        };
        const db = new Pool(dbConfig);
        const jwtSecret = process.env.JWT_SECRET || 'default-secret-key';
        const authService = new AuthenticationService_1.AuthenticationService(db, jwtSecret);
        try {
            const user = await authService.validateToken(token);
            req.user = user;
            return next();
        }
        catch (error) {
            return res.status(401).json({
                error: {
                    code: 'INVALID_TOKEN',
                    message: 'Invalid or expired token',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    catch (error) {
        return res.status(500).json({
            error: {
                code: 'INTERNAL_ERROR',
                message: 'Authentication error',
                timestamp: new Date().toISOString()
            }
        });
    }
};
exports.auth = auth;
//# sourceMappingURL=auth.js.map