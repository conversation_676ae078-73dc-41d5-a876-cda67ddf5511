import { apiClient } from './apiClient';
import { User, AuthToken, LoginCredentials, AuthUser } from '../types';

class AuthService {
  private tokenKey = 'pdm_auth_token';
  private refreshTokenKey = 'pdm_refresh_token';

  async login(username: string, password: string): Promise<AuthUser> {
    const credentials: LoginCredentials = { username, password };
    
    const response = await apiClient.post<AuthUser>('/auth/login', credentials, { skipAuth: true });
    
    if (!response.success) {
      throw new Error(response.error || 'Login failed');
    }

    return response.data!;
  }

  async getCurrentUser(): Promise<User> {
    const token = this.getStoredToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await apiClient.get<User>('/auth/me');

    if (!response.success) {
      throw new Error(response.error || 'Failed to get user info');
    }

    return response.data!;
  }

  async refreshToken(): Promise<AuthToken | null> {
    const token = this.getStoredToken();
    if (!token?.token) {
      return null;
    }

    try {
      const response = await apiClient.post<AuthToken>('/auth/refresh', {
        token: token.token,
      }, { skipAuth: true });

      if (response.success && response.data) {
        const newToken = response.data;
        this.setToken(newToken);
        return newToken;
      }
      return null;
    } catch (error) {
      this.removeToken();
      return null;
    }
  }

  setToken(token: AuthToken): void {
    localStorage.setItem(this.tokenKey, token.token);
    localStorage.setItem(this.refreshTokenKey, token.refreshToken);
  }

  getStoredToken(): AuthToken | null {
    const token = localStorage.getItem(this.tokenKey);
    const refreshToken = localStorage.getItem(this.refreshTokenKey);
    
    if (!token || !refreshToken) {
      return null;
    }

    return {
      token,
      refreshToken,
      expiresAt: '', // We'll handle expiration through API responses
    };
  }

  removeToken(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.refreshTokenKey);
  }

  isTokenExpired(token: AuthToken): boolean {
    if (!token.expiresAt) return false;
    
    const expirationTime = new Date(token.expiresAt).getTime();
    const currentTime = new Date().getTime();
    
    return currentTime >= expirationTime;
  }

  async logout(): Promise<void> {
    const token = this.getStoredToken();
    
    if (token) {
      try {
        await apiClient.post('/auth/logout');
      } catch (error) {
        // Ignore logout errors, just remove local token
        console.warn('Logout request failed:', error);
      }
    }
    
    this.removeToken();
  }
}

export const authService = new AuthService();
