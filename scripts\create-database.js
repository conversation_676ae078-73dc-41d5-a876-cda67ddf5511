#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create the e3_pdm_system database
 */

require('dotenv').config();
const { Pool } = require('pg');

async function createDatabase() {
  // First connect to the default 'postgres' database to create our database
  const config = {
    host: process.env.DB_HOST || 'S-PI-ENGSVR',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: 'postgres', // Connect to default database first
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    connectionTimeoutMillis: 5000,
  };

  const targetDatabase = process.env.DB_NAME || 'e3_pdm_system';

  console.log('🔍 Creating database with the following settings:');
  console.log(`  Host: ${config.host}`);
  console.log(`  Port: ${config.port}`);
  console.log(`  User: ${config.user}`);
  console.log(`  Target Database: ${targetDatabase}`);
  console.log('');

  const pool = new Pool(config);
  
  try {
    console.log('⏳ Connecting to PostgreSQL...');
    const client = await pool.connect();
    
    // Check if database already exists
    console.log('⏳ Checking if database exists...');
    const checkResult = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = $1", 
      [targetDatabase]
    );
    
    if (checkResult.rows.length > 0) {
      console.log(`✅ Database '${targetDatabase}' already exists`);
    } else {
      console.log(`⏳ Creating database '${targetDatabase}'...`);
      
      // Create the database
      await client.query(`CREATE DATABASE ${targetDatabase}`);
      console.log(`✅ Database '${targetDatabase}' created successfully!`);
    }
    
    client.release();
    
    // Now test connection to the new database
    console.log('⏳ Testing connection to new database...');
    const newConfig = { ...config, database: targetDatabase };
    const newPool = new Pool(newConfig);
    
    const newClient = await newPool.connect();
    const testResult = await newClient.query('SELECT NOW() as current_time');
    console.log(`✅ Successfully connected to '${targetDatabase}'`);
    console.log(`  Current time: ${testResult.rows[0].current_time}`);
    
    newClient.release();
    await newPool.end();
    
  } catch (error) {
    console.error('❌ Failed to create database:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused - PostgreSQL server may not be running or accessible');
    } else if (error.code === '28P01') {
      console.log('\n💡 Authentication failed - check username and password');
    } else if (error.code === '3D000') {
      console.log('\n💡 Database connection failed - this is normal when creating the first database');
    } else if (error.code === '42P04') {
      console.log('\n💡 Database already exists');
    }
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

createDatabase();