#!/usr/bin/env node

/**
 * Simple database connection test script
 */

require('dotenv').config();
const { Pool } = require('pg');

const config = {
  host: process.env.DB_HOST || 'S-PI-ENGSVR',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'e3_pdm_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  connectionTimeoutMillis: 5000, // 5 second timeout
};

console.log('🔍 Testing database connection with the following settings:');
console.log(`  Host: ${config.host}`);
console.log(`  Port: ${config.port}`);
console.log(`  Database: ${config.database}`);
console.log(`  User: ${config.user}`);
console.log(`  Password: ${'*'.repeat(config.password.length)}`);
console.log('');

async function testConnection() {
  const pool = new Pool(config);
  
  try {
    console.log('⏳ Attempting to connect...');
    const client = await pool.connect();
    console.log('✅ Connection successful!');
    
    console.log('⏳ Testing query...');
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('✅ Query successful!');
    console.log(`  Current time: ${result.rows[0].current_time}`);
    console.log(`  PostgreSQL version: ${result.rows[0].pg_version}`);
    
    client.release();
    
    // Test if database exists
    console.log('⏳ Checking if database exists...');
    const dbClient = await pool.connect();
    const dbResult = await dbClient.query(
      "SELECT 1 FROM pg_database WHERE datname = $1", 
      [config.database]
    );
    
    if (dbResult.rows.length > 0) {
      console.log('✅ Database exists');
    } else {
      console.log('⚠️  Database does not exist - you may need to create it first');
    }
    
    dbClient.release();
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting tips:');
      console.log('  1. Check if PostgreSQL is running on S-PI-ENGSVR');
      console.log('  2. Verify the server accepts connections on port 5432');
      console.log('  3. Check firewall settings');
      console.log('  4. Verify the hostname resolves correctly');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n💡 Hostname not found:');
      console.log('  1. Check if S-PI-ENGSVR is the correct hostname');
      console.log('  2. Try using the IP address instead');
      console.log('  3. Check DNS resolution');
    } else if (error.code === '28P01') {
      console.log('\n💡 Authentication failed:');
      console.log('  1. Check username and password');
      console.log('  2. Verify user has permission to connect');
    } else if (error.code === '3D000') {
      console.log('\n💡 Database does not exist:');
      console.log('  1. Create the database first');
      console.log('  2. Or update DB_NAME to an existing database');
    }
  } finally {
    await pool.end();
  }
}

testConnection();