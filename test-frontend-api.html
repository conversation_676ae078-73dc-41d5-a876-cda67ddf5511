<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend API</title>
</head>
<body>
    <h1>Test Frontend API Connection</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'testadmin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>Response Status: ${response.status}</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h3>Error: ${error.message}</h3>`;
            }
        }
    </script>
</body>
</html>