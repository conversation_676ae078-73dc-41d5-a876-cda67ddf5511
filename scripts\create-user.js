#!/usr/bin/env node

/**
 * Script to create users in the E3 PDM System
 * Usage: node scripts/create-user.js <username> <email> <password> [role]
 * 
 * Examples:
 *   node scripts/create-user.<NAME_EMAIL> password123 admin
 *   node scripts/create-user.<NAME_EMAIL> mypassword user
 */

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Load environment variables from .env file
require('dotenv').config();

// Database configuration
const dbConfig = {
    host: process.env.DB_HOST || 'S-PI-ENGSVR',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'e3_pdm_system',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
};

async function createUser(username, email, password, role = 'user') {
    const pool = new Pool(dbConfig);

    try {
        console.log('Connecting to database...');

        // Hash password
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Insert user
        const insertQuery = `
      INSERT INTO users (username, email, password_hash, role)
      VALUES ($1, $2, $3, $4)
      RETURNING id, username, email, role, created_at
    `;

        const result = await pool.query(insertQuery, [username, email, passwordHash, role]);
        const userData = result.rows[0];

        console.log('✅ User created successfully!');
        console.log('User Details:');
        console.log(`  ID: ${userData.id}`);
        console.log(`  Username: ${userData.username}`);
        console.log(`  Email: ${userData.email}`);
        console.log(`  Role: ${userData.role}`);
        console.log(`  Created: ${userData.created_at}`);

    } catch (error) {
        console.error('❌ Error creating user:', error.message);

        if (error.code === '23505') { // Unique constraint violation
            if (error.constraint?.includes('username')) {
                console.error('Username already exists. Please choose a different username.');
            } else if (error.constraint?.includes('email')) {
                console.error('Email already exists. Please use a different email address.');
            }
        } else if (error.code === 'ECONNREFUSED') {
            console.error('Cannot connect to database. Make sure PostgreSQL is running and connection details are correct.');
        }

        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
    console.log('Usage: node scripts/create-user.js <username> <email> <password> [role]');
    console.log('');
    console.log('Arguments:');
    console.log('  username  - Unique username for the user');
    console.log('  email     - Email address (must be unique)');
    console.log('  password  - Password for the user');
    console.log('  role      - User role: "admin" or "user" (default: "user")');
    console.log('');
    console.log('Examples:');
    console.log('  node scripts/create-user.<NAME_EMAIL> password123 admin');
    console.log('  node scripts/create-user.<NAME_EMAIL> mypassword user');
    process.exit(1);
}

const [username, email, password, role] = args;

// Validate role
if (role && !['admin', 'user'].includes(role)) {
    console.error('❌ Invalid role. Role must be either "admin" or "user"');
    process.exit(1);
}

// Validate email format (basic)
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
    console.error('❌ Invalid email format');
    process.exit(1);
}

// Validate password length
if (password.length < 6) {
    console.error('❌ Password must be at least 6 characters long');
    process.exit(1);
}

console.log('Creating user with the following details:');
console.log(`  Username: ${username}`);
console.log(`  Email: ${email}`);
console.log(`  Role: ${role || 'user'}`);
console.log('');

createUser(username, email, password, role);