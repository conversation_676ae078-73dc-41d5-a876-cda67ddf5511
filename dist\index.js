"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv = __importStar(require("dotenv"));
dotenv.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const database_1 = require("./database");
const routes_1 = require("./routes");
const errorHandler_1 = require("./middleware/errorHandler");
const logger_1 = __importStar(require("./utils/logger"));
const errors_1 = require("./utils/errors");
const alerting_1 = require("./utils/alerting");
const app = (0, express_1.default)();
const PORT = process.env['PORT'] || 3000;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use((0, morgan_1.default)('combined', { stream: logger_1.morganStream }));
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
app.get('/health', (_req, res) => {
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});
async function initializeApp() {
    try {
        const dbConnection = (0, database_1.getDatabase)(database_1.dbConfig);
        const db = dbConnection.getPool();
        app.use('/api/auth', (0, routes_1.createAuthRoutes)(db));
        app.use('/api/projects', (0, routes_1.createProjectRoutes)(db));
        app.use('/api/files', (0, routes_1.createFileRoutes)(db));
        app.use('/api/versions', (0, routes_1.createVersionRoutes)(db));
        logger_1.default.info('API routes initialized successfully');
    }
    catch (error) {
        const appError = new errors_1.AppError(errors_1.ErrorCode.CONFIGURATION_ERROR, 'Failed to initialize application', 500, 'System initialization failed');
        logger_1.default.error('Failed to initialize database and routes', { error, stack: error instanceof Error ? error.stack : undefined });
        await (0, alerting_1.processErrorWithAlerting)(appError, { initializationError: error });
        process.exit(1);
    }
}
initializeApp();
app.use('*', errorHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
app.listen(PORT, () => {
    logger_1.default.info(`E3 PDM System server running on port ${PORT}`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
    });
});
exports.default = app;
//# sourceMappingURL=index.js.map