"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAuditRoutes = createAuditRoutes;
const express_1 = require("express");
const AuditService_1 = require("../services/AuditService");
const auth_1 = require("../middleware/auth");
const logger_1 = require("../utils/logger");
function createAuditRoutes(db) {
    const router = (0, express_1.Router)();
    const auditService = new AuditService_1.AuditService(db);
    router.get('/audit-trail', auth_1.authenticateToken, async (req, res) => {
        try {
            const { tableName, recordId, action, changedBy, dateFrom, dateTo, limit = '50', offset = '0' } = req.query;
            const query = {
                tableName: tableName,
                recordId: recordId,
                action: action,
                changedBy: changedBy,
                dateFrom: dateFrom ? new Date(dateFrom) : undefined,
                dateTo: dateTo ? new Date(dateTo) : undefined,
                limit: parseInt(limit),
                offset: parseInt(offset)
            };
            const result = await auditService.queryAuditTrail(query);
            res.json({
                success: true,
                data: {
                    entries: result.entries,
                    total: result.total,
                    limit: query.limit,
                    offset: query.offset
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to get audit trail', { error, userId: req.user?.id });
            res.status(500).json({
                success: false,
                error: {
                    code: 'AUDIT_TRAIL_ERROR',
                    message: 'Failed to retrieve audit trail',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/audit-summary', auth_1.authenticateToken, async (req, res) => {
        try {
            const { tableName, recordId } = req.query;
            const summary = await auditService.getAuditSummary(tableName, recordId);
            res.json({
                success: true,
                data: summary
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to get audit summary', { error, userId: req.user?.id });
            res.status(500).json({
                success: false,
                error: {
                    code: 'AUDIT_SUMMARY_ERROR',
                    message: 'Failed to retrieve audit summary',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/change-history/:entityType/:entityId', auth_1.authenticateToken, async (req, res) => {
        try {
            const { entityType, entityId } = req.params;
            if (!['project', 'file', 'version'].includes(entityType)) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_ENTITY_TYPE',
                        message: 'Entity type must be project, file, or version',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const changeHistory = await auditService.getChangeHistory(entityType, entityId);
            res.json({
                success: true,
                data: changeHistory
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to get change history', { error, userId: req.user?.id });
            res.status(500).json({
                success: false,
                error: {
                    code: 'CHANGE_HISTORY_ERROR',
                    message: 'Failed to retrieve change history',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/compliance-report', auth_1.authenticateToken, async (req, res) => {
        try {
            const { dateFrom, dateTo } = req.query;
            if (!dateFrom || !dateTo) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'MISSING_DATE_RANGE',
                        message: 'Both dateFrom and dateTo are required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const fromDate = new Date(dateFrom);
            const toDate = new Date(dateTo);
            if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_DATE_FORMAT',
                        message: 'Invalid date format provided',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            if (fromDate >= toDate) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_DATE_RANGE',
                        message: 'dateFrom must be before dateTo',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const report = await auditService.getComplianceReport(fromDate, toDate);
            res.json({
                success: true,
                data: report
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to generate compliance report', { error, userId: req.user?.id });
            res.status(500).json({
                success: false,
                error: {
                    code: 'COMPLIANCE_REPORT_ERROR',
                    message: 'Failed to generate compliance report',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/audit-log', auth_1.authenticateToken, async (req, res) => {
        try {
            const { tableName, recordId, action, oldValues, newValues, ipAddress, userAgent } = req.body;
            if (!tableName || !recordId || !action) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'MISSING_REQUIRED_FIELDS',
                        message: 'tableName, recordId, and action are required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            if (!['INSERT', 'UPDATE', 'DELETE'].includes(action)) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_ACTION',
                        message: 'Action must be INSERT, UPDATE, or DELETE',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            await auditService.logChange({
                tableName,
                recordId,
                action,
                oldValues,
                newValues,
                changedBy: req.user?.id,
                ipAddress: ipAddress || req.ip,
                userAgent: userAgent || req.get('User-Agent')
            });
            res.json({
                success: true,
                message: 'Audit entry logged successfully'
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to log audit entry', { error, userId: req.user?.id });
            res.status(500).json({
                success: false,
                error: {
                    code: 'AUDIT_LOG_ERROR',
                    message: 'Failed to log audit entry',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    return router;
}
//# sourceMappingURL=auditRoutes.js.map