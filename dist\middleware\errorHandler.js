"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleFileError = exports.handleDatabaseError = exports.validationErrorHandler = exports.notFoundHandler = exports.asyncHandler = exports.errorHandler = void 0;
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../utils/logger"));
const errorHandler = (error, req, res, next) => {
    if (res.headersSent) {
        return next(error);
    }
    let appError;
    if (error instanceof errors_1.AppError) {
        appError = error;
    }
    else {
        if (error.name === 'ValidationError') {
            appError = new errors_1.AppError(errors_1.ErrorCode.VALIDATION_ERROR, error.message, 400, 'Invalid input data provided');
        }
        else if (error.name === 'CastError') {
            appError = new errors_1.AppError(errors_1.ErrorCode.INVALID_INPUT, 'Invalid ID format', 400, 'The provided ID is not valid');
        }
        else if (error.name === 'JsonWebTokenError') {
            appError = new errors_1.AppError(errors_1.ErrorCode.INVALID_TOKEN, 'Invalid token', 401, 'Authentication token is invalid');
        }
        else if (error.name === 'TokenExpiredError') {
            appError = new errors_1.AppError(errors_1.ErrorCode.TOKEN_EXPIRED, 'Token expired', 401, 'Authentication token has expired');
        }
        else if (error.message?.includes('ENOENT')) {
            appError = new errors_1.AppError(errors_1.ErrorCode.FILE_NOT_FOUND, 'File not found', 404, 'The requested file could not be found');
        }
        else if (error.message?.includes('ECONNREFUSED') || error.message?.includes('connection')) {
            appError = new errors_1.AppError(errors_1.ErrorCode.DATABASE_CONNECTION_FAILED, 'Database connection failed', 503, 'Unable to connect to the database');
        }
        else {
            appError = new errors_1.AppError(errors_1.ErrorCode.INTERNAL_SERVER_ERROR, error.message || 'Internal server error', 500, 'An unexpected error occurred', undefined, false);
        }
    }
    const logLevel = appError.statusCode >= 500 ? 'error' : 'warn';
    const logMessage = {
        error: {
            code: appError.code,
            message: appError.message,
            statusCode: appError.statusCode,
            stack: appError.stack,
            isOperational: appError.isOperational,
            details: appError.details,
        },
        request: {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
        },
    };
    logger_1.default[logLevel]('Error occurred', logMessage);
    const errorResponse = {
        error: {
            code: appError.code,
            message: appError.userMessage,
            details: appError.details,
            timestamp: appError.timestamp,
            ...(process.env['NODE_ENV'] === 'development' && {
                stack: appError.stack,
                originalMessage: appError.message,
            }),
        },
    };
    if (appError.statusCode < 500) {
        errorResponse.error.suggestions = (0, errors_1.getRecoverySuggestions)(appError.code);
    }
    res.status(appError.statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const notFoundHandler = (req, _res, next) => {
    const error = new errors_1.AppError(errors_1.ErrorCode.PROJECT_NOT_FOUND, `Route ${req.method} ${req.originalUrl} not found`, 404, 'The requested endpoint was not found');
    next(error);
};
exports.notFoundHandler = notFoundHandler;
const validationErrorHandler = (errors) => {
    const details = errors.reduce((acc, error) => {
        acc[error.path || 'unknown'] = error.message;
        return acc;
    }, {});
    throw new errors_1.AppError(errors_1.ErrorCode.VALIDATION_ERROR, 'Validation failed', 400, 'Please check your input data', { validationErrors: details });
};
exports.validationErrorHandler = validationErrorHandler;
const handleDatabaseError = (error) => {
    if (error.code === '23505') {
        throw new errors_1.AppError(errors_1.ErrorCode.DATABASE_CONSTRAINT_VIOLATION, 'Duplicate entry', 409, 'A record with this information already exists');
    }
    else if (error.code === '23503') {
        throw new errors_1.AppError(errors_1.ErrorCode.DATABASE_CONSTRAINT_VIOLATION, 'Referenced record not found', 409, 'Cannot perform operation due to data dependencies');
    }
    else if (error.code === '23502') {
        throw new errors_1.AppError(errors_1.ErrorCode.VALIDATION_ERROR, 'Required field missing', 400, 'Required information is missing');
    }
    else {
        throw new errors_1.AppError(errors_1.ErrorCode.DATABASE_QUERY_FAILED, error.message || 'Database operation failed', 500, 'Database operation failed');
    }
};
exports.handleDatabaseError = handleDatabaseError;
const handleFileError = (error, fileType) => {
    if (error.code === 'LIMIT_FILE_SIZE') {
        throw new errors_1.AppError(errors_1.ErrorCode.FILE_SIZE_EXCEEDED, 'File size too large', 413, 'The uploaded file exceeds the maximum allowed size');
    }
    else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        throw new errors_1.AppError(errors_1.ErrorCode.INVALID_FILE_FORMAT, 'Unexpected file field', 400, 'Invalid file upload format');
    }
    else if (error.message?.includes('ENOENT')) {
        throw new errors_1.AppError(errors_1.ErrorCode.FILE_NOT_FOUND, 'File not found', 404, 'The requested file could not be found');
    }
    else if (error.message?.includes('corrupted') || error.message?.includes('invalid')) {
        throw new errors_1.AppError(errors_1.ErrorCode.FILE_CORRUPTED, `${fileType || 'File'} appears to be corrupted`, 422, 'The file appears to be corrupted or invalid');
    }
    else {
        throw new errors_1.AppError(errors_1.ErrorCode.FILE_PROCESSING_FAILED, error.message || 'File processing failed', 422, 'Unable to process the uploaded file');
    }
};
exports.handleFileError = handleFileError;
//# sourceMappingURL=errorHandler.js.map