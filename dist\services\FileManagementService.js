"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileManagementService = void 0;
const path = __importStar(require("path"));
const os = __importStar(require("os"));
const util_1 = require("util");
const fs = __importStar(require("fs"));
const writeFile = (0, util_1.promisify)(fs.writeFile);
const unlink = (0, util_1.promisify)(fs.unlink);
class FileManagementService {
    constructor(fileStorageService, fileUploadService, versionControlService, fileParserService, db, auditService) {
        this.fileStorageService = fileStorageService;
        this.fileUploadService = fileUploadService;
        this.versionControlService = versionControlService;
        this.fileParserService = fileParserService;
        this.db = db;
        this.auditService = auditService;
    }
    async uploadFile(file, projectId, uploadedBy, fileType, metadata = {}) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            if (!projectId || projectId.trim().length === 0) {
                throw new Error('Project ID is required');
            }
            if (!uploadedBy || uploadedBy.trim().length === 0) {
                throw new Error('Uploaded by user ID is required');
            }
            const projectResult = await client.query('SELECT id FROM projects WHERE id = $1 AND status = $2', [projectId, 'active']);
            if (projectResult.rows.length === 0) {
                throw new Error(`Project ${projectId} not found or not active`);
            }
            const extractedMetadata = await this.extractMetadata(file);
            const combinedMetadata = { ...extractedMetadata, ...metadata };
            const fileRecord = await this.fileUploadService.processUpload(file, projectId, uploadedBy, fileType, combinedMetadata);
            const fileId = crypto.randomUUID();
            await client.query(`INSERT INTO files (id, project_id, name, original_name, file_type, size, checksum, uploaded_by, uploaded_at, current_version, metadata)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`, [
                fileId,
                projectId,
                fileRecord.name,
                fileRecord.originalName,
                fileType,
                fileRecord.size,
                fileRecord.checksum,
                uploadedBy,
                new Date(),
                '1.0.0',
                JSON.stringify(combinedMetadata)
            ]);
            const versionChanges = {
                description: 'Initial upload',
                modifiedBy: uploadedBy
            };
            const version = await this.versionControlService.createVersion(fileId, versionChanges);
            await this.auditService.logChange({
                tableName: 'files',
                recordId: fileId,
                action: 'INSERT',
                newValues: {
                    id: fileId,
                    project_id: projectId,
                    name: fileRecord.name,
                    original_name: fileRecord.originalName,
                    file_type: fileType,
                    size: fileRecord.size,
                    uploaded_by: uploadedBy,
                    current_version: '1.0.0',
                    metadata: combinedMetadata
                },
                changedBy: uploadedBy
            });
            const updatedFileRecord = {
                ...fileRecord,
                id: fileId,
                currentVersion: version.version,
                versions: [version]
            };
            await client.query('COMMIT');
            return updatedFileRecord;
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async downloadFile(fileId, version) {
        if (!fileId || fileId.trim().length === 0) {
            throw new Error('File ID is required');
        }
        const fileResult = await this.db.query('SELECT * FROM files WHERE id = $1 AND is_deleted = false', [fileId]);
        if (fileResult.rows.length === 0) {
            throw new Error(`File ${fileId} not found`);
        }
        let versionRecord;
        if (version) {
            versionRecord = await this.versionControlService.getVersion(fileId, version);
            if (!versionRecord) {
                throw new Error(`Version ${version} of file ${fileId} not found`);
            }
        }
        else {
            versionRecord = await this.versionControlService.getLatestVersion(fileId);
            if (!versionRecord) {
                throw new Error(`No versions found for file ${fileId}`);
            }
        }
        return this.fileStorageService.getFileStream(versionRecord.filePath);
    }
    async deleteFile(fileId) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            if (!fileId || fileId.trim().length === 0) {
                throw new Error('File ID is required');
            }
            const fileResult = await client.query('SELECT * FROM files WHERE id = $1 AND is_deleted = false', [fileId]);
            if (fileResult.rows.length === 0) {
                throw new Error(`File ${fileId} not found or already deleted`);
            }
            const releasedVersions = await this.versionControlService.getReleasedVersions(fileId);
            if (releasedVersions.length > 0) {
                throw new Error(`Cannot delete file ${fileId} - it has released versions`);
            }
            await client.query('UPDATE files SET is_deleted = true WHERE id = $1', [fileId]);
            await this.auditService.logChange({
                tableName: 'files',
                recordId: fileId,
                action: 'UPDATE',
                oldValues: { is_deleted: false },
                newValues: { is_deleted: true },
                changedBy: 'system'
            });
            await client.query('COMMIT');
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async listFiles(projectId, filters) {
        if (!projectId || projectId.trim().length === 0) {
            throw new Error('Project ID is required');
        }
        let query = `
      SELECT f.*, u.username as uploaded_by_username
      FROM files f
      LEFT JOIN users u ON f.uploaded_by = u.id
      WHERE f.project_id = $1 AND f.is_deleted = false
    `;
        const queryParams = [projectId];
        let paramIndex = 2;
        if (filters?.fileType) {
            query += ` AND f.file_type = $${paramIndex}`;
            queryParams.push(filters.fileType);
            paramIndex++;
        }
        if (filters?.dateFrom) {
            query += ` AND f.uploaded_at >= $${paramIndex}`;
            queryParams.push(filters.dateFrom);
            paramIndex++;
        }
        if (filters?.dateTo) {
            query += ` AND f.uploaded_at <= $${paramIndex}`;
            queryParams.push(filters.dateTo);
            paramIndex++;
        }
        if (filters?.author) {
            query += ` AND u.username ILIKE $${paramIndex}`;
            queryParams.push(`%${filters.author}%`);
            paramIndex++;
        }
        query += ' ORDER BY f.uploaded_at DESC';
        const result = await this.db.query(query, queryParams);
        const fileRecords = [];
        for (const row of result.rows) {
            const versions = await this.versionControlService.getVersionHistory(row.id);
            const fileRecord = {
                id: row.id,
                projectId: row.project_id,
                name: row.name,
                originalName: row.original_name,
                fileType: row.file_type,
                size: parseInt(row.size),
                checksum: row.checksum,
                uploadedBy: row.uploaded_by,
                uploadedAt: row.uploaded_at,
                currentVersion: row.current_version,
                metadata: row.metadata || {},
                versions: versions
            };
            fileRecords.push(fileRecord);
        }
        return fileRecords;
    }
    async extractMetadata(file) {
        if (!file) {
            throw new Error('File is required');
        }
        const fileType = this.fileUploadService.getFileTypeFromExtension(file.originalname);
        if (!fileType) {
            throw new Error('Unable to determine file type from extension');
        }
        const metadata = {
            title: file.originalname,
            author: 'Unknown',
            createdDate: new Date(),
        };
        try {
            const tempFilePath = path.join(os.tmpdir(), `temp_${Date.now()}_${file.originalname}`);
            await writeFile(tempFilePath, file.buffer);
            try {
                switch (fileType) {
                    case 'pdf':
                        const pdfMetadata = await this.fileParserService.parsePDFMetadata(tempFilePath);
                        Object.assign(metadata, pdfMetadata);
                        break;
                    case 'dxf':
                        const dxfMetadata = await this.fileParserService.parseDXFMetadata(tempFilePath);
                        Object.assign(metadata, dxfMetadata);
                        break;
                    case 'e3series':
                        metadata.layers = [];
                        break;
                }
            }
            finally {
                try {
                    await unlink(tempFilePath);
                }
                catch (cleanupError) {
                    console.warn('Failed to clean up temporary file:', cleanupError);
                }
            }
        }
        catch (error) {
            console.warn('Failed to extract metadata:', error);
            switch (fileType) {
                case 'pdf':
                    metadata.pageCount = 1;
                    break;
                case 'dxf':
                    metadata.layers = [];
                    metadata.dimensions = { width: 0, height: 0 };
                    break;
                case 'e3series':
                    metadata.layers = [];
                    break;
            }
        }
        return metadata;
    }
    async verifyFileIntegrity(fileId, version) {
        if (!fileId || fileId.trim().length === 0) {
            throw new Error('File ID is required');
        }
        let versionRecord;
        if (version) {
            versionRecord = await this.versionControlService.getVersion(fileId, version);
            if (!versionRecord) {
                throw new Error(`Version ${version} of file ${fileId} not found`);
            }
        }
        else {
            versionRecord = await this.versionControlService.getLatestVersion(fileId);
            if (!versionRecord) {
                throw new Error(`No versions found for file ${fileId}`);
            }
        }
        try {
            const calculatedChecksum = await this.fileStorageService.calculateChecksumFromFile(versionRecord.filePath);
            const versionResult = await this.db.query('SELECT checksum FROM versions WHERE id = $1', [versionRecord.id]);
            if (versionResult.rows.length === 0) {
                return false;
            }
            const storedChecksum = versionResult.rows[0].checksum;
            return calculatedChecksum === storedChecksum;
        }
        catch (error) {
            return false;
        }
    }
    async getFileStats(fileId) {
        if (!fileId || fileId.trim().length === 0) {
            throw new Error('File ID is required');
        }
        const fileResult = await this.db.query('SELECT size, uploaded_at FROM files WHERE id = $1 AND is_deleted = false', [fileId]);
        if (fileResult.rows.length === 0) {
            throw new Error(`File ${fileId} not found`);
        }
        const fileRecord = fileResult.rows[0];
        const versionStats = await this.versionControlService.getVersionStatistics(fileId);
        const latestVersion = await this.versionControlService.getLatestVersion(fileId);
        return {
            size: parseInt(fileRecord.size),
            createdAt: fileRecord.uploaded_at,
            modifiedAt: latestVersion?.createdAt || fileRecord.uploaded_at,
            versions: versionStats.totalVersions
        };
    }
    async fileExists(fileId, version) {
        if (!fileId || fileId.trim().length === 0) {
            throw new Error('File ID is required');
        }
        try {
            const fileResult = await this.db.query('SELECT id FROM files WHERE id = $1 AND is_deleted = false', [fileId]);
            if (fileResult.rows.length === 0) {
                return false;
            }
            if (version) {
                const versionRecord = await this.versionControlService.getVersion(fileId, version);
                return versionRecord !== null;
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    getSupportedFileTypes() {
        return ['e3series', 'pdf', 'dxf'];
    }
    getMaxFileSize() {
        return 1024 * 1024 * 1024;
    }
    async validateFile(file) {
        if (!file) {
            return {
                isValid: false,
                errors: ['No file provided'],
                warnings: []
            };
        }
        const uploadValidation = await this.fileUploadService.validateFileBeforeUpload(file);
        const warnings = [];
        if (file.originalname.includes(' ')) {
            warnings.push('File name contains spaces, which may cause issues in some systems');
        }
        if (file.originalname.length > 100) {
            warnings.push('File name is quite long, consider using a shorter name');
        }
        return {
            isValid: uploadValidation.isValid,
            errors: uploadValidation.errors,
            warnings
        };
    }
    async createNewVersion(fileId, file, changes, modifiedBy) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const fileResult = await this.db.query('SELECT * FROM files WHERE id = $1 AND is_deleted = false', [fileId]);
            if (fileResult.rows.length === 0) {
                throw new Error(`File ${fileId} not found`);
            }
            const existingFile = fileResult.rows[0];
            const fileType = this.fileUploadService.getFileTypeFromExtension(file.originalname);
            if (fileType !== existingFile.file_type) {
                throw new Error(`File type mismatch. Expected ${existingFile.file_type}, got ${fileType}`);
            }
            const metadata = await this.extractMetadata(file);
            const versionChanges = {
                description: changes,
                modifiedBy: modifiedBy
            };
            const version = await this.versionControlService.createVersion(fileId, versionChanges);
            const checksum = this.fileStorageService.calculateChecksum(file.buffer);
            const fileData = {
                buffer: file.buffer,
                originalName: file.originalname,
                mimetype: file.mimetype,
                size: file.size
            };
            const filePath = await this.fileStorageService.storeFile(existingFile.project_id, fileData, fileId, version.version);
            await client.query('UPDATE versions SET file_size = $1, checksum = $2, file_path = $3 WHERE id = $4', [file.size, checksum, filePath, version.id]);
            await client.query('UPDATE files SET metadata = $1, current_version = $2 WHERE id = $3', [JSON.stringify(metadata), version.version, fileId]);
            const updatedFileResult = await client.query('SELECT * FROM files WHERE id = $1', [fileId]);
            const updatedFile = updatedFileResult.rows[0];
            const versions = await this.versionControlService.getVersionHistory(fileId);
            const fileRecord = {
                id: updatedFile.id,
                projectId: updatedFile.project_id,
                name: updatedFile.name,
                originalName: updatedFile.original_name,
                fileType: updatedFile.file_type,
                size: parseInt(updatedFile.size),
                checksum: updatedFile.checksum,
                uploadedBy: updatedFile.uploaded_by,
                uploadedAt: updatedFile.uploaded_at,
                currentVersion: updatedFile.current_version,
                metadata: updatedFile.metadata || {},
                versions: versions
            };
            await client.query('COMMIT');
            return fileRecord;
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async getFile(fileId) {
        if (!fileId || fileId.trim().length === 0) {
            throw new Error('File ID is required');
        }
        const fileResult = await this.db.query('SELECT * FROM files WHERE id = $1 AND is_deleted = false', [fileId]);
        if (fileResult.rows.length === 0) {
            return null;
        }
        const row = fileResult.rows[0];
        const versions = await this.versionControlService.getVersionHistory(fileId);
        return {
            id: row.id,
            projectId: row.project_id,
            name: row.name,
            originalName: row.original_name,
            fileType: row.file_type,
            size: parseInt(row.size),
            checksum: row.checksum,
            uploadedBy: row.uploaded_by,
            uploadedAt: row.uploaded_at,
            currentVersion: row.current_version,
            metadata: row.metadata || {},
            versions: versions
        };
    }
    async searchFiles(searchTerm, filters) {
        if (!searchTerm || searchTerm.trim().length === 0) {
            throw new Error('Search term is required');
        }
        let query = `
      SELECT f.*, u.username as uploaded_by_username
      FROM files f
      LEFT JOIN users u ON f.uploaded_by = u.id
      WHERE f.is_deleted = false
      AND (f.name ILIKE $1 OR f.original_name ILIKE $1 OR f.metadata::text ILIKE $1)
    `;
        const queryParams = [`%${searchTerm}%`];
        let paramIndex = 2;
        if (filters?.fileType) {
            query += ` AND f.file_type = $${paramIndex}`;
            queryParams.push(filters.fileType);
            paramIndex++;
        }
        if (filters?.dateFrom) {
            query += ` AND f.uploaded_at >= $${paramIndex}`;
            queryParams.push(filters.dateFrom);
            paramIndex++;
        }
        if (filters?.dateTo) {
            query += ` AND f.uploaded_at <= $${paramIndex}`;
            queryParams.push(filters.dateTo);
            paramIndex++;
        }
        query += ' ORDER BY f.uploaded_at DESC LIMIT 100';
        const result = await this.db.query(query, queryParams);
        const fileRecords = [];
        for (const row of result.rows) {
            const versions = await this.versionControlService.getVersionHistory(row.id);
            const fileRecord = {
                id: row.id,
                projectId: row.project_id,
                name: row.name,
                originalName: row.original_name,
                fileType: row.file_type,
                size: parseInt(row.size),
                checksum: row.checksum,
                uploadedBy: row.uploaded_by,
                uploadedAt: row.uploaded_at,
                currentVersion: row.current_version,
                metadata: row.metadata || {},
                versions: versions
            };
            fileRecords.push(fileRecord);
        }
        return fileRecords;
    }
}
exports.FileManagementService = FileManagementService;
//# sourceMappingURL=FileManagementService.js.map