"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDashboardRoutes = createDashboardRoutes;
const express_1 = require("express");
const ProjectManagementService_1 = require("../services/ProjectManagementService");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = __importDefault(require("../utils/logger"));
function createDashboardRoutes(db) {
    const router = (0, express_1.Router)();
    const projectService = new ProjectManagementService_1.ProjectManagementService(db);
    router.get('/stats', auth_1.auth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const user = req.user;
        logger_1.default.debug('Getting dashboard stats for user', { userId: user.id });
        try {
            const projectStats = await projectService.getProjectStats(user.id);
            const totalFiles = 0;
            const recentActivity = 0;
            const dashboardStats = {
                totalProjects: projectStats.totalProjects,
                activeProjects: projectStats.activeProjects,
                totalFiles,
                recentActivity
            };
            logger_1.default.info('Dashboard stats retrieved successfully', {
                userId: user.id,
                stats: dashboardStats
            });
            res.json({
                success: true,
                data: dashboardStats
            });
        }
        catch (error) {
            logger_1.default.error('Failed to get dashboard stats', {
                error: error.message,
                userId: user.id
            });
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve dashboard statistics'
            });
        }
    }));
    return router;
}
//# sourceMappingURL=dashboardRoutes.js.map