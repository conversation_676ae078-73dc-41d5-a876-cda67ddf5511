{"ast": null, "code": "var _jsxFileName = \"D:\\\\GITHUB\\\\PDM\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './components/auth/AuthContext';\nimport { AppStateProvider } from './contexts/AppStateContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { ProtectedRoute } from './components/auth/ProtectedRoute';\nimport { LoginPage } from './components/auth/LoginPage';\nimport { Dashboard } from './components/dashboard/Dashboard';\nimport { ProjectView } from './components/projects/ProjectView';\nimport { FileView } from './components/files/FileView';\nimport { Layout } from './components/common/Layout';\nimport { NotificationSystem } from './components/common/NotificationSystem';\nimport './styles/App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(AppStateProvider, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"app\",\n          children: [/*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Layout, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 26,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 25,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/projects/:projectId\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Layout, {\n                  children: /*#__PURE__*/_jsxDEV(ProjectView, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 33,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/projects/:projectId/files\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Layout, {\n                  children: /*#__PURE__*/_jsxDEV(FileView, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationSystem, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "AppStateProvider", "ThemeProvider", "ProtectedRoute", "LoginPage", "Dashboard", "ProjectView", "FileView", "Layout", "NotificationSystem", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/GITHUB/PDM/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './components/auth/AuthContext';\nimport { AppStateProvider } from './contexts/AppStateContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { ProtectedRoute } from './components/auth/ProtectedRoute';\nimport { LoginPage } from './components/auth/LoginPage';\nimport { Dashboard } from './components/dashboard/Dashboard';\nimport { ProjectView } from './components/projects/ProjectView';\nimport { FileView } from './components/files/FileView';\nimport { Layout } from './components/common/Layout';\nimport { NotificationSystem } from './components/common/NotificationSystem';\nimport './styles/App.css';\n\nconst App: React.FC = () => {\n  return (\n    <ThemeProvider>\n      <AuthProvider>\n        <AppStateProvider>\n          <div className=\"app\">\n            <Routes>\n              <Route path=\"/login\" element={<LoginPage />} />\n              <Route path=\"/\" element={\n                <ProtectedRoute>\n                  <Layout>\n                    <Dashboard />\n                  </Layout>\n                </ProtectedRoute>\n              } />\n              <Route path=\"/projects/:projectId\" element={\n                <ProtectedRoute>\n                  <Layout>\n                    <ProjectView />\n                  </Layout>\n                </ProtectedRoute>\n              } />\n              <Route path=\"/projects/:projectId/files\" element={\n                <ProtectedRoute>\n                  <Layout>\n                    <FileView />\n                  </Layout>\n                </ProtectedRoute>\n              } />\n              <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n            </Routes>\n            <NotificationSystem />\n          </div>\n        </AppStateProvider>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACT,aAAa;IAAAW,QAAA,eACZF,OAAA,CAACX,YAAY;MAAAa,QAAA,eACXF,OAAA,CAACV,gBAAgB;QAAAY,QAAA,eACfF,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBF,OAAA,CAACd,MAAM;YAAAgB,QAAA,gBACLF,OAAA,CAACb,KAAK;cAACiB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEL,OAAA,CAACP,SAAS;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CT,OAAA,CAACb,KAAK;cAACiB,IAAI,EAAC,GAAG;cAACC,OAAO,eACrBL,OAAA,CAACR,cAAc;gBAAAU,QAAA,eACbF,OAAA,CAACH,MAAM;kBAAAK,QAAA,eACLF,OAAA,CAACN,SAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJT,OAAA,CAACb,KAAK;cAACiB,IAAI,EAAC,sBAAsB;cAACC,OAAO,eACxCL,OAAA,CAACR,cAAc;gBAAAU,QAAA,eACbF,OAAA,CAACH,MAAM;kBAAAK,QAAA,eACLF,OAAA,CAACL,WAAW;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJT,OAAA,CAACb,KAAK;cAACiB,IAAI,EAAC,4BAA4B;cAACC,OAAO,eAC9CL,OAAA,CAACR,cAAc;gBAAAU,QAAA,eACbF,OAAA,CAACH,MAAM;kBAAAK,QAAA,eACLF,OAAA,CAACJ,QAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJT,OAAA,CAACb,KAAK;cAACiB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAACZ,QAAQ;gBAACsB,EAAE,EAAC,GAAG;gBAACC,OAAO;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACTT,OAAA,CAACF,kBAAkB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACG,EAAA,GArCIX,GAAa;AAuCnB,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}