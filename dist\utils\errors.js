"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRecoverySuggestions = exports.DatabaseError = exports.FileProcessingError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.AppError = exports.ErrorCode = void 0;
var ErrorCode;
(function (ErrorCode) {
    ErrorCode["AUTHENTICATION_FAILED"] = "AUTHENTICATION_FAILED";
    ErrorCode["AUTHORIZATION_FAILED"] = "AUTHORIZATION_FAILED";
    ErrorCode["TOKEN_EXPIRED"] = "TOKEN_EXPIRED";
    ErrorCode["INVALID_TOKEN"] = "INVALID_TOKEN";
    ErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorCode["MISSING_REQUIRED_FIELD"] = "MISSING_REQUIRED_FIELD";
    ErrorCode["FILE_NOT_FOUND"] = "FILE_NOT_FOUND";
    ErrorCode["FILE_UPLOAD_FAILED"] = "FILE_UPLOAD_FAILED";
    ErrorCode["FILE_PROCESSING_FAILED"] = "FILE_PROCESSING_FAILED";
    ErrorCode["INVALID_FILE_FORMAT"] = "INVALID_FILE_FORMAT";
    ErrorCode["FILE_SIZE_EXCEEDED"] = "FILE_SIZE_EXCEEDED";
    ErrorCode["FILE_CORRUPTED"] = "FILE_CORRUPTED";
    ErrorCode["UNSUPPORTED_E3_VERSION"] = "UNSUPPORTED_E3_VERSION";
    ErrorCode["PROJECT_NOT_FOUND"] = "PROJECT_NOT_FOUND";
    ErrorCode["PROJECT_ACCESS_DENIED"] = "PROJECT_ACCESS_DENIED";
    ErrorCode["PROJECT_CREATION_FAILED"] = "PROJECT_CREATION_FAILED";
    ErrorCode["PROJECT_UPDATE_FAILED"] = "PROJECT_UPDATE_FAILED";
    ErrorCode["PROJECT_DELETION_FAILED"] = "PROJECT_DELETION_FAILED";
    ErrorCode["VERSION_NOT_FOUND"] = "VERSION_NOT_FOUND";
    ErrorCode["VERSION_LOCKED"] = "VERSION_LOCKED";
    ErrorCode["VERSION_CONFLICT"] = "VERSION_CONFLICT";
    ErrorCode["VERSION_CREATION_FAILED"] = "VERSION_CREATION_FAILED";
    ErrorCode["DATABASE_CONNECTION_FAILED"] = "DATABASE_CONNECTION_FAILED";
    ErrorCode["DATABASE_QUERY_FAILED"] = "DATABASE_QUERY_FAILED";
    ErrorCode["DATABASE_CONSTRAINT_VIOLATION"] = "DATABASE_CONSTRAINT_VIOLATION";
    ErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ErrorCode["SERVICE_UNAVAILABLE"] = "SERVICE_UNAVAILABLE";
    ErrorCode["CONFIGURATION_ERROR"] = "CONFIGURATION_ERROR";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class AppError extends Error {
    constructor(code, message, statusCode, userMessage, details, isOperational = true) {
        super(message);
        this.code = code;
        this.statusCode = statusCode || this.getDefaultStatusCode(code);
        this.isOperational = isOperational;
        this.details = details;
        this.userMessage = userMessage || this.getDefaultUserMessage(code);
        this.timestamp = new Date().toISOString();
        Error.captureStackTrace(this, this.constructor);
    }
    getDefaultStatusCode(code) {
        const statusCodes = {
            [ErrorCode.AUTHENTICATION_FAILED]: 401,
            [ErrorCode.AUTHORIZATION_FAILED]: 403,
            [ErrorCode.TOKEN_EXPIRED]: 401,
            [ErrorCode.INVALID_TOKEN]: 401,
            [ErrorCode.VALIDATION_ERROR]: 400,
            [ErrorCode.INVALID_INPUT]: 400,
            [ErrorCode.MISSING_REQUIRED_FIELD]: 400,
            [ErrorCode.FILE_NOT_FOUND]: 404,
            [ErrorCode.FILE_UPLOAD_FAILED]: 422,
            [ErrorCode.FILE_PROCESSING_FAILED]: 422,
            [ErrorCode.INVALID_FILE_FORMAT]: 400,
            [ErrorCode.FILE_SIZE_EXCEEDED]: 413,
            [ErrorCode.FILE_CORRUPTED]: 422,
            [ErrorCode.UNSUPPORTED_E3_VERSION]: 422,
            [ErrorCode.PROJECT_NOT_FOUND]: 404,
            [ErrorCode.PROJECT_ACCESS_DENIED]: 403,
            [ErrorCode.PROJECT_CREATION_FAILED]: 422,
            [ErrorCode.PROJECT_UPDATE_FAILED]: 422,
            [ErrorCode.PROJECT_DELETION_FAILED]: 422,
            [ErrorCode.VERSION_NOT_FOUND]: 404,
            [ErrorCode.VERSION_LOCKED]: 409,
            [ErrorCode.VERSION_CONFLICT]: 409,
            [ErrorCode.VERSION_CREATION_FAILED]: 422,
            [ErrorCode.DATABASE_CONNECTION_FAILED]: 503,
            [ErrorCode.DATABASE_QUERY_FAILED]: 500,
            [ErrorCode.DATABASE_CONSTRAINT_VIOLATION]: 409,
            [ErrorCode.INTERNAL_SERVER_ERROR]: 500,
            [ErrorCode.SERVICE_UNAVAILABLE]: 503,
            [ErrorCode.CONFIGURATION_ERROR]: 500,
        };
        return statusCodes[code] || 500;
    }
    getDefaultUserMessage(code) {
        const userMessages = {
            [ErrorCode.AUTHENTICATION_FAILED]: 'Invalid username or password. Please try again.',
            [ErrorCode.AUTHORIZATION_FAILED]: 'You do not have permission to perform this action.',
            [ErrorCode.TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
            [ErrorCode.INVALID_TOKEN]: 'Invalid authentication token. Please log in again.',
            [ErrorCode.VALIDATION_ERROR]: 'The provided data is invalid. Please check your input.',
            [ErrorCode.INVALID_INPUT]: 'Invalid input provided. Please check your data and try again.',
            [ErrorCode.MISSING_REQUIRED_FIELD]: 'Required fields are missing. Please complete all required information.',
            [ErrorCode.FILE_NOT_FOUND]: 'The requested file could not be found.',
            [ErrorCode.FILE_UPLOAD_FAILED]: 'File upload failed. Please try again.',
            [ErrorCode.FILE_PROCESSING_FAILED]: 'Unable to process the file. Please check the file format and try again.',
            [ErrorCode.INVALID_FILE_FORMAT]: 'Unsupported file format. Please upload a valid e3 series, PDF, or DXF file.',
            [ErrorCode.FILE_SIZE_EXCEEDED]: 'File size exceeds the maximum allowed limit.',
            [ErrorCode.FILE_CORRUPTED]: 'The file appears to be corrupted. Please try uploading a different file.',
            [ErrorCode.UNSUPPORTED_E3_VERSION]: 'This e3 series file version is not supported. Please use a compatible version.',
            [ErrorCode.PROJECT_NOT_FOUND]: 'The requested project could not be found.',
            [ErrorCode.PROJECT_ACCESS_DENIED]: 'You do not have access to this project.',
            [ErrorCode.PROJECT_CREATION_FAILED]: 'Failed to create project. Please try again.',
            [ErrorCode.PROJECT_UPDATE_FAILED]: 'Failed to update project. Please try again.',
            [ErrorCode.PROJECT_DELETION_FAILED]: 'Failed to delete project. Please try again.',
            [ErrorCode.VERSION_NOT_FOUND]: 'The requested version could not be found.',
            [ErrorCode.VERSION_LOCKED]: 'This version is locked and cannot be modified.',
            [ErrorCode.VERSION_CONFLICT]: 'Version conflict detected. Please refresh and try again.',
            [ErrorCode.VERSION_CREATION_FAILED]: 'Failed to create new version. Please try again.',
            [ErrorCode.DATABASE_CONNECTION_FAILED]: 'Database connection failed. Please try again later.',
            [ErrorCode.DATABASE_QUERY_FAILED]: 'Database operation failed. Please try again.',
            [ErrorCode.DATABASE_CONSTRAINT_VIOLATION]: 'Data constraint violation. Please check your input.',
            [ErrorCode.INTERNAL_SERVER_ERROR]: 'An internal server error occurred. Please try again later.',
            [ErrorCode.SERVICE_UNAVAILABLE]: 'Service is temporarily unavailable. Please try again later.',
            [ErrorCode.CONFIGURATION_ERROR]: 'System configuration error. Please contact support.',
        };
        return userMessages[code] || 'An unexpected error occurred. Please try again.';
    }
    toJSON() {
        return {
            error: {
                code: this.code,
                message: this.userMessage,
                details: this.details,
                timestamp: this.timestamp,
            },
        };
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message, details) {
        super(ErrorCode.VALIDATION_ERROR, message, 400, undefined, details);
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(ErrorCode.AUTHENTICATION_FAILED, message, 401);
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Access denied') {
        super(ErrorCode.AUTHORIZATION_FAILED, message, 403);
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends AppError {
    constructor(resource, id) {
        const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
        const errorCode = resource.toLowerCase().includes('project') ? ErrorCode.PROJECT_NOT_FOUND : ErrorCode.FILE_NOT_FOUND;
        super(errorCode, message, 404);
    }
}
exports.NotFoundError = NotFoundError;
class FileProcessingError extends AppError {
    constructor(message, code = ErrorCode.FILE_PROCESSING_FAILED, details) {
        super(code, message, 422, undefined, details);
    }
}
exports.FileProcessingError = FileProcessingError;
class DatabaseError extends AppError {
    constructor(message, originalError) {
        super(ErrorCode.DATABASE_QUERY_FAILED, message, 500, undefined, originalError ? { originalError: originalError.message } : undefined);
    }
}
exports.DatabaseError = DatabaseError;
const getRecoverySuggestions = (code) => {
    const suggestions = {
        [ErrorCode.AUTHENTICATION_FAILED]: [
            'Verify your username and password',
            'Check if your account is active',
            'Contact your administrator if the problem persists',
        ],
        [ErrorCode.FILE_CORRUPTED]: [
            'Try uploading the file again',
            'Verify the file is not damaged',
            'Use a different version of the file if available',
            'Contact support if the file should be valid',
        ],
        [ErrorCode.UNSUPPORTED_E3_VERSION]: [
            'Check the supported e3 series versions in the documentation',
            'Convert the file to a supported version',
            'Contact support for version compatibility information',
        ],
        [ErrorCode.FILE_SIZE_EXCEEDED]: [
            'Compress the file if possible',
            'Split large projects into smaller components',
            'Contact your administrator to increase file size limits',
        ],
        [ErrorCode.VERSION_LOCKED]: [
            'Contact the user who locked the version',
            'Create a new version based on the locked version',
            'Wait for the lock to be released',
        ],
        [ErrorCode.DATABASE_CONNECTION_FAILED]: [
            'Check your internet connection',
            'Try again in a few moments',
            'Contact support if the problem persists',
        ],
    };
    return suggestions[code] || ['Try again later', 'Contact support if the problem persists'];
};
exports.getRecoverySuggestions = getRecoverySuggestions;
//# sourceMappingURL=errors.js.map