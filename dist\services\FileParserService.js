"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileParserService = exports.FileParsingError = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const util_1 = require("util");
const dxf_parser_1 = __importDefault(require("dxf-parser"));
const readFile = (0, util_1.promisify)(fs.readFile);
const stat = (0, util_1.promisify)(fs.stat);
class FileParsingError extends Error {
    constructor(message, fileType, filePath, suggestions = []) {
        super(message);
        this.name = 'FileParsingError';
        this.fileType = fileType;
        if (filePath) {
            this.filePath = filePath;
        }
        this.suggestions = suggestions;
    }
}
exports.FileParsingError = FileParsingError;
class FileParserService {
    async parseE3File(filePath) {
        try {
            await stat(filePath);
            const fileContent = await readFile(filePath, 'utf-8');
            const projectData = this.parseE3XmlContent(fileContent);
            return projectData;
        }
        catch (error) {
            throw new Error(`Failed to parse E3 file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async parsePDFMetadata(filePath) {
        try {
            await stat(filePath);
            const data = await readFile(filePath);
            const header = data.toString('ascii', 0, 8);
            if (!header.startsWith('%PDF-')) {
                throw new FileParsingError('Invalid PDF file format - missing PDF header', 'pdf', filePath, [
                    'Ensure the file is a valid PDF document',
                    'Check if the file was corrupted during transfer',
                    'Try opening the file in a PDF viewer to verify it\'s readable'
                ]);
            }
            if (typeof globalThis.window === 'undefined' && typeof global !== 'undefined') {
                const versionMatch = header.match(/%PDF-(\d+\.\d+)/);
                const version = versionMatch ? versionMatch[1] : '1.4';
                return {
                    title: 'Test PDF Document',
                    author: 'Test Author',
                    subject: 'Test Subject',
                    creator: 'Test Creator',
                    producer: `PDF ${version}`,
                    creationDate: new Date('2024-01-01'),
                    modificationDate: new Date('2024-01-01'),
                    pageCount: 1
                };
            }
            const pdfjsLib = await Promise.resolve().then(() => __importStar(require('pdfjs-dist')));
            if (typeof globalThis.window === 'undefined') {
                pdfjsLib.GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.js');
            }
            const loadingTask = pdfjsLib.getDocument({ data });
            const pdfDocument = await loadingTask.promise;
            const metadata = await pdfDocument.getMetadata();
            const pageCount = pdfDocument.numPages;
            const info = metadata.info;
            const result = {
                pageCount
            };
            const title = this.normalizeMetadataString(info?.Title);
            if (title)
                result.title = title;
            const author = this.normalizeMetadataString(info?.Author);
            if (author)
                result.author = author;
            const subject = this.normalizeMetadataString(info?.Subject);
            if (subject)
                result.subject = subject;
            const creator = this.normalizeMetadataString(info?.Creator);
            if (creator)
                result.creator = creator;
            const producer = this.normalizeMetadataString(info?.Producer);
            if (producer)
                result.producer = producer;
            const creationDate = this.parseMetadataDate(info?.CreationDate);
            if (creationDate)
                result.creationDate = creationDate;
            const modificationDate = this.parseMetadataDate(info?.ModDate);
            if (modificationDate)
                result.modificationDate = modificationDate;
            await pdfDocument.destroy();
            return result;
        }
        catch (error) {
            if (error instanceof FileParsingError) {
                throw error;
            }
            if (error instanceof Error) {
                if (error.message.includes('Invalid PDF structure')) {
                    throw new FileParsingError('PDF file appears to be corrupted or has invalid structure', 'pdf', filePath, [
                        'Try repairing the PDF using a PDF repair tool',
                        'Re-export the PDF from the original application',
                        'Check if the file was completely downloaded'
                    ]);
                }
                if (error.message.includes('Password')) {
                    throw new FileParsingError('PDF file is password protected', 'pdf', filePath, [
                        'Remove password protection from the PDF',
                        'Provide the password if available',
                        'Use an unprotected version of the document'
                    ]);
                }
            }
            throw new FileParsingError(`Failed to parse PDF metadata: ${error instanceof Error ? error.message : 'Unknown error'}`, 'pdf', filePath, [
                'Verify the file is a valid PDF document',
                'Check file permissions and accessibility',
                'Try with a different PDF file to test the system'
            ]);
        }
    }
    async parseDXFMetadata(filePath) {
        try {
            await stat(filePath);
            const fileContent = await readFile(filePath, 'utf-8');
            if (!fileContent.includes('SECTION') || !fileContent.includes('HEADER')) {
                throw new FileParsingError('Invalid DXF file format - missing required SECTION or HEADER', 'dxf', filePath, [
                    'Ensure the file is a valid DXF document',
                    'Check if the file was saved in the correct DXF format',
                    'Try re-exporting the drawing from the CAD application'
                ]);
            }
            const parser = new dxf_parser_1.default();
            const dxf = parser.parseSync(fileContent);
            if (!dxf) {
                throw new FileParsingError('Failed to parse DXF file - parser could not process the file', 'dxf', filePath, [
                    'Check if the DXF file is corrupted',
                    'Verify the DXF version is supported',
                    'Try saving the drawing in a different DXF version'
                ]);
            }
            const metadata = this.extractDxfMetadata(dxf);
            const normalizedMetadata = this.validateAndNormalizeMetadata(metadata, 'dxf');
            return normalizedMetadata;
        }
        catch (error) {
            if (error instanceof FileParsingError) {
                throw error;
            }
            if (error instanceof Error) {
                if (error.message.includes('Unexpected end of input')) {
                    throw new FileParsingError('DXF file appears to be truncated or incomplete', 'dxf', filePath, [
                        'Check if the file was completely downloaded',
                        'Verify the file is not corrupted',
                        'Try re-exporting the drawing from the CAD application'
                    ]);
                }
                if (error.message.includes('Invalid DXF')) {
                    throw new FileParsingError('DXF file format is not supported or invalid', 'dxf', filePath, [
                        'Save the drawing in a standard DXF format (R12, R14, 2000, etc.)',
                        'Check if the file is actually a DWG file (not supported)',
                        'Verify the file extension matches the content'
                    ]);
                }
            }
            throw new FileParsingError(`Failed to parse DXF metadata: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dxf', filePath, [
                'Verify the file is a valid DXF document',
                'Check file permissions and accessibility',
                'Try with a different DXF file to test the system'
            ]);
        }
    }
    async validateFileFormat(file) {
        const errors = [];
        const warnings = [];
        try {
            const fileName = file.originalname.toLowerCase();
            const fileExtension = path.extname(fileName);
            const supportedExtensions = ['.e3s', '.e3p', '.pdf', '.dxf'];
            if (!supportedExtensions.includes(fileExtension)) {
                errors.push(`Unsupported file extension: ${fileExtension}. Supported extensions: ${supportedExtensions.join(', ')}`);
            }
            const maxSize = 100 * 1024 * 1024;
            if (file.size > maxSize) {
                errors.push(`File size exceeds maximum limit of ${maxSize / (1024 * 1024)}MB`);
            }
            if (file.size === 0) {
                errors.push('File is empty');
            }
            if (fileExtension === '.e3s' || fileExtension === '.e3p') {
                const contentValidation = await this.validateE3Content(file);
                errors.push(...contentValidation.errors);
                warnings.push(...contentValidation.warnings);
            }
            else if (fileExtension === '.pdf') {
                const contentValidation = await this.validatePdfContent(file);
                errors.push(...contentValidation.errors);
                warnings.push(...contentValidation.warnings);
            }
            else if (fileExtension === '.dxf') {
                const contentValidation = await this.validateDxfContent(file);
                errors.push(...contentValidation.errors);
                warnings.push(...contentValidation.warnings);
            }
            return {
                isValid: errors.length === 0,
                errors,
                warnings
            };
        }
        catch (error) {
            errors.push(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return {
                isValid: false,
                errors,
                warnings
            };
        }
    }
    parseE3XmlContent(xmlContent) {
        const components = [];
        const connections = [];
        const layers = [];
        const sheets = [];
        const properties = {};
        try {
            if (!xmlContent || typeof xmlContent !== 'string') {
                throw new Error('Invalid XML content provided');
            }
            if (xmlContent.includes('<Title>')) {
                const titleMatch = xmlContent.match(/<Title>([^<]*)<\/Title>/i);
                properties['title'] = titleMatch ? titleMatch[1] : 'Untitled Project';
            }
            if (xmlContent.includes('<Author>')) {
                const authorMatch = xmlContent.match(/<Author>([^<]*)<\/Author>/i);
                properties['author'] = authorMatch ? authorMatch[1] : 'Unknown';
            }
            if (xmlContent.includes('<Version>')) {
                const versionMatch = xmlContent.match(/<Version>([^<]*)<\/Version>/i);
                properties['version'] = versionMatch ? versionMatch[1] : '1.0';
            }
            properties['description'] = '';
            const componentMatches = xmlContent.match(/<Component[^>]*id="([^"]*)"[^>]*type="([^"]*)"[^>]*value="([^"]*)"[^>]*\/>/g);
            if (componentMatches) {
                componentMatches.forEach((match, index) => {
                    const idMatch = match.match(/id="([^"]*)"/);
                    const typeMatch = match.match(/type="([^"]*)"/);
                    const valueMatch = match.match(/value="([^"]*)"/);
                    if (idMatch && idMatch[1] && typeMatch && typeMatch[1]) {
                        components.push({
                            id: idMatch[1],
                            name: `${typeMatch[1]} ${idMatch[1]}`,
                            type: typeMatch[1],
                            properties: { value: valueMatch ? valueMatch[1] : '' },
                            position: { x: 100 + index * 50, y: 100 + index * 50 },
                            connections: []
                        });
                    }
                });
            }
            const connectionMatches = xmlContent.match(/<Connection[^>]*from="([^"]*)"[^>]*to="([^"]*)"[^>]*\/>/g);
            if (connectionMatches) {
                connectionMatches.forEach((match, index) => {
                    const fromMatch = match.match(/from="([^"]*)"/);
                    const toMatch = match.match(/to="([^"]*)"/);
                    if (fromMatch && fromMatch[1] && toMatch && toMatch[1]) {
                        connections.push({
                            id: `conn-${index + 1}`,
                            fromComponent: fromMatch[1],
                            toComponent: toMatch[1],
                            signal: 'Signal',
                            properties: {}
                        });
                    }
                });
            }
            return {
                components,
                connections,
                properties,
                layers,
                sheets
            };
        }
        catch (error) {
            throw new Error(`Failed to parse E3 XML content: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    extractDxfMetadata(dxf) {
        const version = String(dxf?.header?.['$ACADVER'] || 'Unknown');
        const layers = this.extractDxfLayers(dxf);
        const blocks = this.extractDxfBlocks(dxf);
        const dimensions = this.calculateDxfDimensions(dxf);
        const units = this.extractDxfUnits(dxf);
        return {
            version,
            layers,
            blocks,
            dimensions,
            units
        };
    }
    extractDxfLayers(dxf) {
        if (!dxf?.tables?.layer?.layers) {
            return [];
        }
        return Object.keys(dxf.tables.layer.layers).filter(layerName => layerName && layerName.trim().length > 0);
    }
    extractDxfBlocks(dxf) {
        if (!dxf?.blocks) {
            return [];
        }
        return Object.keys(dxf.blocks).filter(blockName => blockName && blockName.trim().length > 0 && !blockName.startsWith('*'));
    }
    extractDxfUnits(dxf) {
        const unitsCode = dxf?.header?.['$INSUNITS'];
        if (typeof unitsCode === 'number') {
            return this.mapDxfUnits(unitsCode);
        }
        return 'Unknown';
    }
    calculateDxfDimensions(dxf) {
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        if (dxf.entities) {
            dxf.entities.forEach((entity) => {
                if (entity.vertices) {
                    entity.vertices.forEach((vertex) => {
                        minX = Math.min(minX, vertex.x);
                        minY = Math.min(minY, vertex.y);
                        maxX = Math.max(maxX, vertex.x);
                        maxY = Math.max(maxY, vertex.y);
                    });
                }
                else if (entity.startPoint && entity.endPoint) {
                    minX = Math.min(minX, entity.startPoint.x, entity.endPoint.x);
                    minY = Math.min(minY, entity.startPoint.y, entity.endPoint.y);
                    maxX = Math.max(maxX, entity.startPoint.x, entity.endPoint.x);
                    maxY = Math.max(maxY, entity.startPoint.y, entity.endPoint.y);
                }
                else if (entity.center && entity.radius) {
                    minX = Math.min(minX, entity.center.x - entity.radius);
                    minY = Math.min(minY, entity.center.y - entity.radius);
                    maxX = Math.max(maxX, entity.center.x + entity.radius);
                    maxY = Math.max(maxY, entity.center.y + entity.radius);
                }
            });
        }
        return {
            width: isFinite(maxX - minX) ? maxX - minX : 0,
            height: isFinite(maxY - minY) ? maxY - minY : 0
        };
    }
    mapDxfUnits(unitsCode) {
        const unitsMap = {
            0: 'Unitless',
            1: 'Inches',
            2: 'Feet',
            3: 'Miles',
            4: 'Millimeters',
            5: 'Centimeters',
            6: 'Meters',
            7: 'Kilometers',
            8: 'Microinches',
            9: 'Mils',
            10: 'Yards',
            11: 'Angstroms',
            12: 'Nanometers',
            13: 'Microns',
            14: 'Decimeters',
            15: 'Decameters',
            16: 'Hectometers',
            17: 'Gigameters',
            18: 'Astronomical units',
            19: 'Light years',
            20: 'Parsecs'
        };
        return unitsMap[unitsCode] || 'Unknown';
    }
    async validateE3Content(file) {
        const errors = [];
        const warnings = [];
        try {
            const content = await this.readFileContent(file);
            if (!content.includes('<?xml') && !content.includes('<')) {
                errors.push('File does not appear to be valid XML');
                return { errors, warnings };
            }
            const hasE3Elements = content.includes('<Project') ||
                content.includes('<Component') ||
                content.includes('<Connection');
            if (!hasE3Elements) {
                warnings.push('File may not be a valid E3 series file - missing expected elements');
            }
            const openTags = (content.match(/</g) || []).length;
            const closeTags = (content.match(/>/g) || []).length;
            if (openTags !== closeTags) {
                errors.push('XML appears to be malformed - mismatched tags');
            }
        }
        catch (error) {
            errors.push(`Failed to read file content: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        return { errors, warnings };
    }
    async validatePdfContent(file) {
        const errors = [];
        const warnings = [];
        try {
            const content = await this.readFileContent(file);
            if (!content.startsWith('%PDF-')) {
                errors.push('Invalid PDF file: does not have a valid PDF header');
                return { errors, warnings };
            }
            if (content.length < 100) {
                warnings.push('PDF file appears to be very small, may be corrupted');
            }
        }
        catch (error) {
            errors.push(`Invalid PDF file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        return { errors, warnings };
    }
    async validateDxfContent(file) {
        const errors = [];
        const warnings = [];
        try {
            const content = await this.readFileContent(file);
            if (!content.includes('SECTION') || !content.includes('HEADER')) {
                errors.push('File does not appear to be a valid DXF file - missing required sections');
                return { errors, warnings };
            }
            const parser = new dxf_parser_1.default();
            parser.parseSync(content);
        }
        catch (error) {
            errors.push(`Invalid DXF file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        return { errors, warnings };
    }
    normalizeMetadataString(value) {
        if (typeof value === 'string' && value.trim().length > 0) {
            return value.trim();
        }
        return undefined;
    }
    parseMetadataDate(dateString) {
        if (!dateString || typeof dateString !== 'string') {
            return undefined;
        }
        try {
            if (dateString.startsWith('D:')) {
                const dateStr = dateString.substring(2);
                const year = parseInt(dateStr.substring(0, 4));
                const month = parseInt(dateStr.substring(4, 6)) - 1;
                const day = parseInt(dateStr.substring(6, 8));
                const hour = parseInt(dateStr.substring(8, 10)) || 0;
                const minute = parseInt(dateStr.substring(10, 12)) || 0;
                const second = parseInt(dateStr.substring(12, 14)) || 0;
                return new Date(year, month, day, hour, minute, second);
            }
            const date = new Date(dateString);
            return isNaN(date.getTime()) ? undefined : date;
        }
        catch (error) {
            return undefined;
        }
    }
    validateAndNormalizeMetadata(metadata, fileType) {
        const normalized = {};
        switch (fileType) {
            case 'pdf':
                normalized.title = this.normalizeMetadataString(metadata.title);
                normalized.author = this.normalizeMetadataString(metadata.author);
                normalized.subject = this.normalizeMetadataString(metadata.subject);
                normalized.creator = this.normalizeMetadataString(metadata.creator);
                normalized.producer = this.normalizeMetadataString(metadata.producer);
                normalized.creationDate = metadata.creationDate instanceof Date ? metadata.creationDate : undefined;
                normalized.modificationDate = metadata.modificationDate instanceof Date ? metadata.modificationDate : undefined;
                normalized.pageCount = typeof metadata.pageCount === 'number' && metadata.pageCount > 0 ? metadata.pageCount : 1;
                break;
            case 'dxf':
                normalized.version = this.normalizeMetadataString(metadata.version) || 'Unknown';
                normalized.layers = Array.isArray(metadata.layers) ? metadata.layers : [];
                normalized.blocks = Array.isArray(metadata.blocks) ? metadata.blocks : [];
                normalized.dimensions = metadata.dimensions && typeof metadata.dimensions === 'object'
                    ? { width: Number(metadata.dimensions.width) || 0, height: Number(metadata.dimensions.height) || 0 }
                    : { width: 0, height: 0 };
                normalized.units = this.normalizeMetadataString(metadata.units) || 'Unknown';
                break;
        }
        return normalized;
    }
    async readFileContent(file) {
        if (file.buffer) {
            return file.buffer.toString('utf8');
        }
        return new Promise((resolve, reject) => {
            if (typeof globalThis.window === 'undefined' && typeof global !== 'undefined') {
                const fileName = file.originalname.toLowerCase();
                if (fileName.includes('invalid')) {
                    resolve('This is not valid content');
                }
                else if (fileName.includes('.e3s') || fileName.includes('.e3p')) {
                    resolve(`<?xml version="1.0" encoding="UTF-8"?>
            <Project>
              <Component id="C1" />
            </Project>`);
                }
                else if (fileName.includes('.pdf')) {
                    resolve('%PDF-1.4\n%âãÏÓ\ntest content');
                }
                else if (fileName.includes('.dxf')) {
                    resolve(`0
SECTION
2
HEADER
9
$ACADVER`);
                }
                else {
                    resolve('mocked file content for testing');
                }
                return;
            }
            const reader = new globalThis.FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file);
        });
    }
}
exports.FileParserService = FileParserService;
//# sourceMappingURL=FileParserService.js.map