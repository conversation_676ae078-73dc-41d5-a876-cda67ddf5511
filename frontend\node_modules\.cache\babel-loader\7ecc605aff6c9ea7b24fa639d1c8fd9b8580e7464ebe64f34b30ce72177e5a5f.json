{"ast": null, "code": "var _jsxFileName = \"D:\\\\GITHUB\\\\PDM\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext(undefined);\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [theme, setThemeState] = useState(() => {\n    // Check localStorage first\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme;\n    }\n\n    // Check system preference\n    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n      return 'dark';\n    }\n    return 'light';\n  });\n  useEffect(() => {\n    // Apply theme to document\n    document.documentElement.setAttribute('data-theme', theme);\n\n    // Save to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = e => {\n      // Only auto-switch if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('theme');\n      if (!savedTheme) {\n        setThemeState(e.matches ? 'dark' : 'light');\n      }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return () => {\n      mediaQuery.removeEventListener('change', handleChange);\n    };\n  }, []);\n  const toggleTheme = () => {\n    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n  const setTheme = newTheme => {\n    setThemeState(newTheme);\n  };\n  const value = {\n    theme,\n    toggleTheme,\n    setTheme\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"Nr7hA4w2ekmQChmwAPOknbz7y0I=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ThemeContext", "undefined", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "theme", "setThemeState", "savedTheme", "localStorage", "getItem", "window", "matchMedia", "matches", "document", "documentElement", "setAttribute", "setItem", "mediaQuery", "handleChange", "e", "addEventListener", "removeEventListener", "toggleTheme", "prevTheme", "setTheme", "newTheme", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/GITHUB/PDM/frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setThemeState] = useState<Theme>(() => {\n    // Check localStorage first\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      return savedTheme;\n    }\n    \n    // Check system preference\n    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n      return 'dark';\n    }\n    \n    return 'light';\n  });\n\n  useEffect(() => {\n    // Apply theme to document\n    document.documentElement.setAttribute('data-theme', theme);\n    \n    // Save to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      // Only auto-switch if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('theme');\n      if (!savedTheme) {\n        setThemeState(e.matches ? 'dark' : 'light');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    \n    return () => {\n      mediaQuery.removeEventListener('change', handleChange);\n    };\n  }, []);\n\n  const toggleTheme = () => {\n    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    toggleTheme,\n    setTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU9E,MAAMC,YAAY,gBAAGN,aAAa,CAA+BO,SAAS,CAAC;AAE3E,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGT,UAAU,CAACK,YAAY,CAAC;EACxC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAYrB,OAAO,MAAMI,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC3E,MAAM,CAACC,KAAK,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAQ,MAAM;IACnD;IACA,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAU;IACzD,IAAIF,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;;IAEA;IACA,IAAIG,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,EAAE;MAClF,OAAO,MAAM;IACf;IAEA,OAAO,OAAO;EAChB,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACd;IACAqB,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,EAAEV,KAAK,CAAC;;IAE1D;IACAG,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAEX,KAAK,CAAC;EACtC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEXb,SAAS,CAAC,MAAM;IACd;IACA,MAAMyB,UAAU,GAAGP,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IAEpE,MAAMO,YAAY,GAAIC,CAAsB,IAAK;MAC/C;MACA,MAAMZ,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAChD,IAAI,CAACF,UAAU,EAAE;QACfD,aAAa,CAACa,CAAC,CAACP,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;MAC7C;IACF,CAAC;IAEDK,UAAU,CAACG,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAEnD,OAAO,MAAM;MACXD,UAAU,CAACI,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxBhB,aAAa,CAACiB,SAAS,IAAIA,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EACtE,CAAC;EAED,MAAMC,QAAQ,GAAIC,QAAe,IAAK;IACpCnB,aAAa,CAACmB,QAAQ,CAAC;EACzB,CAAC;EAED,MAAMC,KAAuB,GAAG;IAC9BrB,KAAK;IACLiB,WAAW;IACXE;EACF,CAAC;EAED,oBACE7B,OAAA,CAACC,YAAY,CAAC+B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,EACjCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAC3B,GAAA,CA9DWF,aAA2C;AAAA8B,EAAA,GAA3C9B,aAA2C;AAAA,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}