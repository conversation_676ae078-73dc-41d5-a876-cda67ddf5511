import { Router, Request, Response } from 'express';
import { Pool } from 'pg';
import { ProjectManagementService } from '../services/ProjectManagementService';
import { ProjectHierarchyService } from '../services/ProjectHierarchyService';
import { ProjectMetadataService } from '../services/ProjectMetadataService';
import { ProjectArchivalService } from '../services/ProjectArchivalService';
import { auth } from '../middleware/auth';
import { asyncHandler, handleDatabaseError } from '../middleware/errorHandler';
import { ValidationError, NotFoundError, AppError, ErrorCode } from '../utils/errors';
import { processErrorWithAlerting } from '../utils/alerting';
import logger from '../utils/logger';
import { ProjectData } from '../types';

export function createProjectRoutes(db: Pool): Router {
  const router = Router();
  
  // Initialize services
  const projectService = new ProjectManagementService(db);
  const hierarchyService = new ProjectHierarchyService(db);
  const metadataService = new ProjectMetadataService(db);
  const archivalService = new ProjectArchivalService(db);

  /**
   * GET /api/projects
   * List all projects for the current user
   */
  router.get('/', auth, asyncHandler(async (req: Request, res: Response) => {
    const user = (req as any).user;

    logger.debug('Listing projects for user', { userId: user.id });

    try {
      // Get query parameters for pagination
      const limit = parseInt(req.query.limit as string) || 10;
      const page = parseInt(req.query.page as string) || 1;

      const projects = await projectService.listProjects(user.id);

      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedProjects = projects.slice(startIndex, endIndex);
      const totalPages = Math.ceil(projects.length / limit);

      logger.info('Projects listed successfully', {
        userId: user.id,
        projectCount: projects.length,
        page,
        limit
      });

      res.json({
        success: true,
        data: paginatedProjects.map(project => ({
          id: project.id,
          name: project.name,
          description: project.description,
          status: project.status,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          createdBy: project.createdBy,
          metadata: project.metadata
        })),
        total: projects.length,
        page,
        limit,
        totalPages
      });
    } catch (error: any) {
      const appError = new AppError(
        ErrorCode.PROJECT_NOT_FOUND,
        `Failed to list projects: ${error.message}`,
        500,
        'Unable to retrieve projects'
      );

      await processErrorWithAlerting(appError, { userId: user.id, operation: 'listProjects' });
      throw appError;
    }
  }));

  /**
   * POST /api/projects
   * Create a new project
   */
  router.post('/', auth, asyncHandler(async (req: Request, res: Response) => {
    const user = (req as any).user;
    const projectData: ProjectData = req.body;

    logger.debug('Creating project', { userId: user.id, projectName: projectData.name });

    // Validate required fields
    if (!projectData.name || projectData.name.trim().length === 0) {
      throw new ValidationError('Project name is required', { field: 'name' });
    }

    try {
      const project = await projectService.createProject(projectData, user.id);

      logger.info('Project created successfully', { 
        userId: user.id, 
        projectId: project.id,
        projectName: project.name 
      });

      res.status(201).json({
        project: {
          id: project.id,
          name: project.name,
          description: project.description,
          status: project.status,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          createdBy: project.createdBy,
          metadata: project.metadata
        }
      });
    } catch (error: any) {
      if (error.code === '23505') { // Unique constraint violation
        handleDatabaseError(error);
      }
      
      const appError = new AppError(
        ErrorCode.PROJECT_CREATION_FAILED,
        `Failed to create project: ${error.message}`,
        500,
        'Unable to create project'
      );
      
      await processErrorWithAlerting(appError, { 
        userId: user.id, 
        operation: 'createProject',
        projectData: { name: projectData.name, description: projectData.description }
      });
      throw appError;
    }
  }));

  /**
   * GET /api/projects/:projectId
   * Get a specific project
   */
  router.get('/:projectId', auth, async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const user = (req as any).user;

      const project = await projectService.getProject(projectId, user.id);

      if (!project) {
        return res.status(404).json({
          error: {
            code: 'PROJECT_NOT_FOUND',
            message: 'Project not found',
            timestamp: new Date().toISOString()
          }
        });
      }

      res.json({
        project: {
          id: project.id,
          name: project.name,
          description: project.description,
          status: project.status,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          createdBy: project.createdBy,
          metadata: project.metadata,
          permissions: project.permissions
        }
      });

    } catch (error) {
      console.error('Get project error:', error);
      res.status(500).json({
        error: {
          code: 'GET_PROJECT_FAILED',
          message: 'Failed to get project',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * PUT /api/projects/:projectId
   * Update a project
   */
  router.put('/:projectId', auth, async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const user = (req as any).user;
      const updates: Partial<ProjectData> = req.body;

      const project = await projectService.updateProject(projectId, updates, user.id);

      res.json({
        project: {
          id: project.id,
          name: project.name,
          description: project.description,
          status: project.status,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          createdBy: project.createdBy,
          metadata: project.metadata
        }
      });

    } catch (error) {
      console.error('Update project error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'PROJECT_NOT_FOUND',
            message: 'Project not found',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'UPDATE_PROJECT_FAILED',
            message: 'Failed to update project',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  /**
   * DELETE /api/projects/:projectId
   * Archive a project
   */
  router.delete('/:projectId', auth, async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const user = (req as any).user;

      await projectService.archiveProject(projectId, user.id);

      res.json({
        message: 'Project archived successfully'
      });

    } catch (error) {
      console.error('Archive project error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'PROJECT_NOT_FOUND',
            message: 'Project not found',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'ARCHIVE_PROJECT_FAILED',
            message: 'Failed to archive project',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  /**
   * GET /api/projects/:projectId/hierarchy
   * Get project folder hierarchy
   */
  router.get('/:projectId/hierarchy', auth, async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const user = (req as any).user;

      const hierarchy = await hierarchyService.getProjectHierarchy(projectId, user.id);

      res.json({
        hierarchy
      });

    } catch (error) {
      console.error('Get hierarchy error:', error);
      res.status(500).json({
        error: {
          code: 'GET_HIERARCHY_FAILED',
          message: 'Failed to get project hierarchy',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * POST /api/projects/:projectId/folders
   * Create a new folder in project
   */
  router.post('/:projectId/folders', auth, async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const user = (req as any).user;
      const { name, parentId } = req.body;

      if (!name || name.trim().length === 0) {
        return res.status(400).json({
          error: {
            code: 'INVALID_FOLDER_DATA',
            message: 'Folder name is required',
            timestamp: new Date().toISOString()
          }
        });
      }

      const folder = await hierarchyService.createFolder(projectId, name, user.id, parentId);

      res.status(201).json({
        folder: {
          id: folder.id,
          name: folder.name,
          path: folder.path,
          parentId: folder.parentId,
          createdAt: folder.createdAt,
          createdBy: folder.createdBy
        }
      });

    } catch (error) {
      console.error('Create folder error:', error);
      res.status(500).json({
        error: {
          code: 'CREATE_FOLDER_FAILED',
          message: 'Failed to create folder',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * PUT /api/projects/:projectId/metadata
   * Update project metadata
   */
  router.put('/:projectId/metadata', auth, async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const user = (req as any).user;
      const { metadata } = req.body;

      await metadataService.updateProjectMetadata(projectId, metadata, user.id);

      res.json({
        message: 'Project metadata updated successfully'
      });

    } catch (error) {
      console.error('Update metadata error:', error);
      res.status(500).json({
        error: {
          code: 'UPDATE_METADATA_FAILED',
          message: 'Failed to update project metadata',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  return router;
}
