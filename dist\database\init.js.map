{"version": 3, "file": "init.js", "sourceRoot": "", "sources": ["../../src/database/init.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4ES,gDAAkB;AArE3B,+CAAiC;AAGjC,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,6CAA4D;AAC5D,6CAAsD;AAEtD,KAAK,UAAU,kBAAkB;IAC/B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI,CAAC;QAEH,MAAM,EAAE,GAAG,+BAAkB,CAAC,WAAW,CAAC,qBAAQ,CAAC,CAAC;QAGpD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;QAC9C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAGhD,MAAM,gBAAgB,GAAG,IAAA,mCAAsB,EAAC,EAAE,CAAC,CAAC;QAGpD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,SAAS,EAAE,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAG5D,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,MAAM,gBAAgB,CAAC,OAAO,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,SAAS,EAAE,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,+BAA+B,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,yBAAyB,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAEnE,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,CAAC,EAAE,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,SAAS,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;QACjB,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IAEtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,kBAAkB,EAAE,CAAC;AACvB,CAAC"}