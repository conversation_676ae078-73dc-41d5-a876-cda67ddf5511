"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDashboardRoutes = exports.createAuditRoutes = exports.createVersionRoutes = exports.createFileRoutes = exports.createProjectRoutes = exports.createAuthRoutes = void 0;
var authRoutes_1 = require("./authRoutes");
Object.defineProperty(exports, "createAuthRoutes", { enumerable: true, get: function () { return authRoutes_1.createAuthRoutes; } });
var projectRoutes_1 = require("./projectRoutes");
Object.defineProperty(exports, "createProjectRoutes", { enumerable: true, get: function () { return projectRoutes_1.createProjectRoutes; } });
var fileRoutes_1 = require("./fileRoutes");
Object.defineProperty(exports, "createFileRoutes", { enumerable: true, get: function () { return fileRoutes_1.createFileRoutes; } });
var versionRoutes_1 = require("./versionRoutes");
Object.defineProperty(exports, "createVersionRoutes", { enumerable: true, get: function () { return versionRoutes_1.createVersionRoutes; } });
var auditRoutes_1 = require("./auditRoutes");
Object.defineProperty(exports, "createAuditRoutes", { enumerable: true, get: function () { return auditRoutes_1.createAuditRoutes; } });
var dashboardRoutes_1 = require("./dashboardRoutes");
Object.defineProperty(exports, "createDashboardRoutes", { enumerable: true, get: function () { return dashboardRoutes_1.createDashboardRoutes; } });
//# sourceMappingURL=index.js.map