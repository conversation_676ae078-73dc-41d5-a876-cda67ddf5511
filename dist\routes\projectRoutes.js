"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createProjectRoutes = createProjectRoutes;
const express_1 = require("express");
const ProjectManagementService_1 = require("../services/ProjectManagementService");
const ProjectHierarchyService_1 = require("../services/ProjectHierarchyService");
const ProjectMetadataService_1 = require("../services/ProjectMetadataService");
const ProjectArchivalService_1 = require("../services/ProjectArchivalService");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const errors_1 = require("../utils/errors");
const alerting_1 = require("../utils/alerting");
const logger_1 = __importDefault(require("../utils/logger"));
function createProjectRoutes(db) {
    const router = (0, express_1.Router)();
    const projectService = new ProjectManagementService_1.ProjectManagementService(db);
    const hierarchyService = new ProjectHierarchyService_1.ProjectHierarchyService(db);
    const metadataService = new ProjectMetadataService_1.ProjectMetadataService(db);
    const archivalService = new ProjectArchivalService_1.ProjectArchivalService(db);
    router.get('/', auth_1.auth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const user = req.user;
        logger_1.default.debug('Listing projects for user', { userId: user.id });
        try {
            const projects = await projectService.listProjects(user.id);
            logger_1.default.info('Projects listed successfully', {
                userId: user.id,
                projectCount: projects.length
            });
            res.json({
                projects: projects.map(project => ({
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata
                }))
            });
        }
        catch (error) {
            const appError = new errors_1.AppError(errors_1.ErrorCode.PROJECT_NOT_FOUND, `Failed to list projects: ${error.message}`, 500, 'Unable to retrieve projects');
            await (0, alerting_1.processErrorWithAlerting)(appError, { userId: user.id, operation: 'listProjects' });
            throw appError;
        }
    }));
    router.post('/', auth_1.auth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const user = req.user;
        const projectData = req.body;
        logger_1.default.debug('Creating project', { userId: user.id, projectName: projectData.name });
        if (!projectData.name || projectData.name.trim().length === 0) {
            throw new errors_1.ValidationError('Project name is required', { field: 'name' });
        }
        try {
            const project = await projectService.createProject(projectData, user.id);
            logger_1.default.info('Project created successfully', {
                userId: user.id,
                projectId: project.id,
                projectName: project.name
            });
            res.status(201).json({
                project: {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata
                }
            });
        }
        catch (error) {
            if (error.code === '23505') {
                (0, errorHandler_1.handleDatabaseError)(error);
            }
            const appError = new errors_1.AppError(errors_1.ErrorCode.PROJECT_CREATION_FAILED, `Failed to create project: ${error.message}`, 500, 'Unable to create project');
            await (0, alerting_1.processErrorWithAlerting)(appError, {
                userId: user.id,
                operation: 'createProject',
                projectData: { name: projectData.name, description: projectData.description }
            });
            throw appError;
        }
    }));
    router.get('/:projectId', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const project = await projectService.getProject(projectId, user.id);
            if (!project) {
                return res.status(404).json({
                    error: {
                        code: 'PROJECT_NOT_FOUND',
                        message: 'Project not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            res.json({
                project: {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata,
                    permissions: project.permissions
                }
            });
        }
        catch (error) {
            console.error('Get project error:', error);
            res.status(500).json({
                error: {
                    code: 'GET_PROJECT_FAILED',
                    message: 'Failed to get project',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.put('/:projectId', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const updates = req.body;
            const project = await projectService.updateProject(projectId, updates, user.id);
            res.json({
                project: {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata
                }
            });
        }
        catch (error) {
            console.error('Update project error:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({
                    error: {
                        code: 'PROJECT_NOT_FOUND',
                        message: 'Project not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            else {
                res.status(500).json({
                    error: {
                        code: 'UPDATE_PROJECT_FAILED',
                        message: 'Failed to update project',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
    });
    router.delete('/:projectId', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            await projectService.archiveProject(projectId, user.id);
            res.json({
                message: 'Project archived successfully'
            });
        }
        catch (error) {
            console.error('Archive project error:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({
                    error: {
                        code: 'PROJECT_NOT_FOUND',
                        message: 'Project not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            else {
                res.status(500).json({
                    error: {
                        code: 'ARCHIVE_PROJECT_FAILED',
                        message: 'Failed to archive project',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
    });
    router.get('/:projectId/hierarchy', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const hierarchy = await hierarchyService.getProjectHierarchy(projectId, user.id);
            res.json({
                hierarchy
            });
        }
        catch (error) {
            console.error('Get hierarchy error:', error);
            res.status(500).json({
                error: {
                    code: 'GET_HIERARCHY_FAILED',
                    message: 'Failed to get project hierarchy',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/:projectId/folders', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const { name, parentId } = req.body;
            if (!name || name.trim().length === 0) {
                return res.status(400).json({
                    error: {
                        code: 'INVALID_FOLDER_DATA',
                        message: 'Folder name is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const folder = await hierarchyService.createFolder(projectId, name, user.id, parentId);
            res.status(201).json({
                folder: {
                    id: folder.id,
                    name: folder.name,
                    path: folder.path,
                    parentId: folder.parentId,
                    createdAt: folder.createdAt,
                    createdBy: folder.createdBy
                }
            });
        }
        catch (error) {
            console.error('Create folder error:', error);
            res.status(500).json({
                error: {
                    code: 'CREATE_FOLDER_FAILED',
                    message: 'Failed to create folder',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.put('/:projectId/metadata', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const { metadata } = req.body;
            await metadataService.updateProjectMetadata(projectId, metadata, user.id);
            res.json({
                message: 'Project metadata updated successfully'
            });
        }
        catch (error) {
            console.error('Update metadata error:', error);
            res.status(500).json({
                error: {
                    code: 'UPDATE_METADATA_FAILED',
                    message: 'Failed to update project metadata',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    return router;
}
//# sourceMappingURL=projectRoutes.js.map