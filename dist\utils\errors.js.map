{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": ";;;AAKA,IAAY,SA2CX;AA3CD,WAAY,SAAS;IAEnB,4DAA+C,CAAA;IAC/C,0DAA6C,CAAA;IAC7C,4CAA+B,CAAA;IAC/B,4CAA+B,CAAA;IAG/B,kDAAqC,CAAA;IACrC,4CAA+B,CAAA;IAC/B,8DAAiD,CAAA;IAGjD,8CAAiC,CAAA;IACjC,sDAAyC,CAAA;IACzC,8DAAiD,CAAA;IACjD,wDAA2C,CAAA;IAC3C,sDAAyC,CAAA;IACzC,8CAAiC,CAAA;IACjC,8DAAiD,CAAA;IAGjD,oDAAuC,CAAA;IACvC,4DAA+C,CAAA;IAC/C,gEAAmD,CAAA;IACnD,4DAA+C,CAAA;IAC/C,gEAAmD,CAAA;IAGnD,oDAAuC,CAAA;IACvC,8CAAiC,CAAA;IACjC,kDAAqC,CAAA;IACrC,gEAAmD,CAAA;IAGnD,sEAAyD,CAAA;IACzD,4DAA+C,CAAA;IAC/C,4EAA+D,CAAA;IAG/D,4DAA+C,CAAA;IAC/C,wDAA2C,CAAA;IAC3C,wDAA2C,CAAA;AAC7C,CAAC,EA3CW,SAAS,yBAAT,SAAS,QA2CpB;AAED,MAAa,QAAS,SAAQ,KAAK;IAQjC,YACE,IAAe,EACf,OAAe,EACf,UAAmB,EACnB,WAAoB,EACpB,OAA6B,EAC7B,gBAAyB,IAAI;QAE7B,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAG1C,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAEO,oBAAoB,CAAC,IAAe;QAC1C,MAAM,WAAW,GAA8B;YAE7C,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,GAAG;YACtC,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,GAAG;YACrC,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,GAAG;YAC9B,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,GAAG;YAG9B,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,GAAG;YACjC,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,GAAG;YAC9B,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,GAAG;YAGvC,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG;YAC/B,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,GAAG;YACnC,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,GAAG;YACvC,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,GAAG;YACpC,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,GAAG;YACnC,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG;YAC/B,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,GAAG;YAGvC,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,GAAG;YAClC,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,GAAG;YACtC,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,GAAG;YACxC,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,GAAG;YACtC,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,GAAG;YAGxC,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,GAAG;YAClC,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG;YAC/B,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,GAAG;YACjC,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,GAAG;YAGxC,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE,GAAG;YAC3C,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,GAAG;YACtC,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE,GAAG;YAG9C,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,GAAG;YACtC,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,GAAG;YACpC,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,GAAG;SACrC,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;IAClC,CAAC;IAEO,qBAAqB,CAAC,IAAe;QAC3C,MAAM,YAAY,GAA8B;YAC9C,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,iDAAiD;YACpF,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,oDAAoD;YACtF,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,gDAAgD;YAC3E,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,oDAAoD;YAE/E,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,wDAAwD;YACtF,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,+DAA+D;YAC1F,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,wEAAwE;YAE5G,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,wCAAwC;YACpE,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,uCAAuC;YACvE,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,yEAAyE;YAC7G,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,6EAA6E;YAC9G,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,8CAA8C;YAC9E,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,0EAA0E;YACtG,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,gFAAgF;YAEpH,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,2CAA2C;YAC1E,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,yCAAyC;YAC5E,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,6CAA6C;YAClF,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,6CAA6C;YAChF,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,6CAA6C;YAElF,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,2CAA2C;YAC1E,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,gDAAgD;YAC5E,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,0DAA0D;YACxF,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,iDAAiD;YAEtF,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE,qDAAqD;YAC7F,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,8CAA8C;YACjF,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE,qDAAqD;YAEhG,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,4DAA4D;YAC/F,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,6DAA6D;YAC9F,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,qDAAqD;SACvF,CAAC;QAEF,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,iDAAiD,CAAC;IACjF,CAAC;IAEM,MAAM;QACX,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,IAAI,CAAC,WAAW;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B;SACF,CAAC;IACJ,CAAC;CACF;AAlID,4BAkIC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,SAAS,CAAC,gBAAgB,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;CACF;AAJD,0CAIC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,uBAAuB;QACnD,KAAK,CAAC,SAAS,CAAC,qBAAqB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,eAAe;QAC3C,KAAK,CAAC,SAAS,CAAC,oBAAoB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,QAAgB,EAAE,EAAW;QACvC,MAAM,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,YAAY,CAAC;QAErF,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC;QACtH,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;CACF;AAPD,sCAOC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,OAAe,EAAE,OAAkB,SAAS,CAAC,sBAAsB,EAAE,OAA6B;QAC5G,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe,EAAE,aAAqB;QAChD,KAAK,CACH,SAAS,CAAC,qBAAqB,EAC/B,OAAO,EACP,GAAG,EACH,SAAS,EACT,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CACrE,CAAC;IACJ,CAAC;CACF;AAVD,sCAUC;AAGM,MAAM,sBAAsB,GAAG,CAAC,IAAe,EAAY,EAAE;IAClE,MAAM,WAAW,GAAyC;QACxD,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE;YACjC,mCAAmC;YACnC,iCAAiC;YACjC,oDAAoD;SACrD;QACD,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YAC1B,8BAA8B;YAC9B,gCAAgC;YAChC,kDAAkD;YAClD,6CAA6C;SAC9C;QACD,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE;YAClC,6DAA6D;YAC7D,yCAAyC;YACzC,uDAAuD;SACxD;QACD,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE;YAC9B,+BAA+B;YAC/B,8CAA8C;YAC9C,yDAAyD;SAC1D;QACD,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YAC1B,yCAAyC;YACzC,kDAAkD;YAClD,kCAAkC;SACnC;QACD,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE;YACtC,gCAAgC;YAChC,4BAA4B;YAC5B,yCAAyC;SAC1C;KACF,CAAC;IAEF,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,yCAAyC,CAAC,CAAC;AAC7F,CAAC,CAAC;AApCW,QAAA,sBAAsB,0BAoCjC"}