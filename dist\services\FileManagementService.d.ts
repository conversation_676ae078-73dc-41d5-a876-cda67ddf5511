import { Pool } from 'pg';
import { FileStorageService } from './FileStorageService';
import { FileUploadService } from './FileUploadService';
import { VersionControlService } from './VersionControlService';
import { FileParserService } from './FileParserService';
import { FileRecord, FileMetadata, FileFilters } from '../types';
import { FileManagementService as IFileManagementService } from '../types/services';
import { AuditService } from './AuditService';
export declare class FileManagementService implements IFileManagementService {
    private fileStorageService;
    private fileUploadService;
    private versionControlService;
    private fileParserService;
    private db;
    private auditService;
    constructor(fileStorageService: FileStorageService, fileUploadService: FileUploadService, versionControlService: VersionControlService, fileParserService: FileParserService, db: Pool, auditService: AuditService);
    uploadFile(file: Express.Multer.File, projectId: string, uploadedBy: string, fileType: 'e3series' | 'pdf' | 'dxf', metadata?: FileMetadata): Promise<FileRecord>;
    downloadFile(fileId: string, version?: string): Promise<NodeJS.ReadableStream>;
    deleteFile(fileId: string): Promise<void>;
    listFiles(projectId: string, filters?: FileFilters): Promise<FileRecord[]>;
    extractMetadata(file: Express.Multer.File): Promise<FileMetadata>;
    verifyFileIntegrity(fileId: string, version?: string): Promise<boolean>;
    getFileStats(fileId: string): Promise<{
        size: number;
        createdAt: Date;
        modifiedAt: Date;
        versions: number;
    }>;
    fileExists(fileId: string, version?: string): Promise<boolean>;
    getSupportedFileTypes(): ('e3series' | 'pdf' | 'dxf')[];
    getMaxFileSize(): number;
    validateFile(file: Express.Multer.File): Promise<{
        isValid: boolean;
        errors: string[];
        warnings: string[];
    }>;
    createNewVersion(fileId: string, file: Express.Multer.File, changes: string, modifiedBy: string): Promise<FileRecord>;
    getFile(fileId: string): Promise<FileRecord | null>;
    searchFiles(searchTerm: string, filters?: FileFilters): Promise<FileRecord[]>;
}
//# sourceMappingURL=FileManagementService.d.ts.map