{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:18:23:1823'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:18:23:1823'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:20:56:2056'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:20:56:2056'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:04:284'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:04:284'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:18:2818'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:18:2818'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:23:2823'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:23:2823'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:29:2829'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:29:2829'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:29:28:2928'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:29:28:2928'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:29:48:2948'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:29:48:2948'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:38:21:3821'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:38:21:3821'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:39:12:3912'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:39:12:3912'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:39:26:3926'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:39:26:3926'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:40:20:4020'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:40:20:4020'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:41:21:4121'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:41:22:4122'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:41:43:4143'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:41:43:4143'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:42:05:425'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:42:05:425'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:42:36:4236'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:42:36:4236'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:02:432'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:03:433'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:09:439'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:09:439'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:10:4310'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:11:4311'
}
