{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:18:23:1823'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:18:23:1823'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:20:56:2056'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:20:56:2056'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:04:284'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:04:284'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:18:2818'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:18:2818'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:23:2823'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:23:2823'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:28:29:2829'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:28:29:2829'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:29:28:2928'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:29:28:2928'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:29:48:2948'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:29:48:2948'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:38:21:3821'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:38:21:3821'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:39:12:3912'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:39:12:3912'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:39:26:3926'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:39:26:3926'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:40:20:4020'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:40:20:4020'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:41:21:4121'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:41:22:4122'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:41:43:4143'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:41:43:4143'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:42:05:425'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:42:05:425'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:42:36:4236'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:42:36:4236'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:02:432'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:03:433'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:09:439'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:09:439'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:10:4310'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:43:11:4311'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:44:19:4419'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:44:19:4419'
}
{
  error: {
    code: 'PROJECT_NOT_FOUND',
    message: 'Route GET /favicon.ico not found',
    statusCode: 404,
    stack: 'Error: Route GET /favicon.ico not found\n' +
      '    at notFoundHandler (D:\\GITHUB\\PDM\\src\\middleware\\errorHandler.ts:140:17)\n' +
      '    at Layer.handle [as handle_request] (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
      '    at trim_prefix (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
      '    at D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
      '    at param (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:365:14)\n' +
      '    at param (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:376:14)\n' +
      '    at Function.process_params (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:421:3)\n' +
      '    at next (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
      '    at urlencodedParser (D:\\GITHUB\\PDM\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n' +
      '    at Layer.handle [as handle_request] (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\layer.js:95:5)',
    isOperational: true,
    details: undefined
  },
  request: {
    method: 'GET',
    url: '/favicon.ico',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: undefined
  },
  level: 'warn',
  message: 'Error occurred',
  timestamp: '2025-07-24 23:44:53:4453'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:44:54:4454'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:44:54:4454'
}
{
  error: {
    code: 'PROJECT_NOT_FOUND',
    message: 'Route GET /main.1e4e049c48ca6f7b499d.hot-update.json not found',
    statusCode: 404,
    stack: 'Error: Route GET /main.1e4e049c48ca6f7b499d.hot-update.json not found\n' +
      '    at notFoundHandler (D:\\GITHUB\\PDM\\src\\middleware\\errorHandler.ts:140:17)\n' +
      '    at Layer.handle [as handle_request] (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
      '    at trim_prefix (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
      '    at D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
      '    at param (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:365:14)\n' +
      '    at param (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:376:14)\n' +
      '    at Function.process_params (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:421:3)\n' +
      '    at next (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
      '    at urlencodedParser (D:\\GITHUB\\PDM\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n' +
      '    at Layer.handle [as handle_request] (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\layer.js:95:5)',
    isOperational: true,
    details: undefined
  },
  request: {
    method: 'GET',
    url: '/main.1e4e049c48ca6f7b499d.hot-update.json',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: undefined
  },
  level: 'warn',
  message: 'Error occurred',
  timestamp: '2025-07-24 23:45:10:4510'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:45:11:4511'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:45:11:4511'
}
{
  error: {
    code: 'PROJECT_NOT_FOUND',
    message: 'Route GET /main.1e4e049c48ca6f7b499d.hot-update.json not found',
    statusCode: 404,
    stack: 'Error: Route GET /main.1e4e049c48ca6f7b499d.hot-update.json not found\n' +
      '    at notFoundHandler (D:\\GITHUB\\PDM\\src\\middleware\\errorHandler.ts:140:17)\n' +
      '    at Layer.handle [as handle_request] (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
      '    at trim_prefix (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
      '    at D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
      '    at param (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:365:14)\n' +
      '    at param (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:376:14)\n' +
      '    at Function.process_params (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:421:3)\n' +
      '    at next (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
      '    at urlencodedParser (D:\\GITHUB\\PDM\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n' +
      '    at Layer.handle [as handle_request] (D:\\GITHUB\\PDM\\node_modules\\express\\lib\\router\\layer.js:95:5)',
    isOperational: true,
    details: undefined
  },
  request: {
    method: 'GET',
    url: '/main.1e4e049c48ca6f7b499d.hot-update.json',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: undefined
  },
  level: 'warn',
  message: 'Error occurred',
  timestamp: '2025-07-24 23:45:14:4514'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:45:15:4515'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:45:15:4515'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:48:23:4823'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:48:23:4823'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:49:05:495'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:49:05:495'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:49:14:4914'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:49:14:4914'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:49:24:4924'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:49:24:4924'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:50:22:5022'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:50:22:5022'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:29:5029'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:30:5030'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:39:5039'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:40:5040'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:42:5042'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:42:5042'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:43:5043'
}
{
  userId: '43ba9f4a-ce23-4bd4-b87d-a1e7abfdb9a9',
  projectCount: 0,
  level: 'info',
  message: 'Projects listed successfully',
  timestamp: '2025-07-24 23:50:43:5043'
}
{
  message: 'API routes initialized successfully',
  level: 'info',
  timestamp: '2025-07-24 23:56:06:566'
}
{
  port: '3001',
  environment: 'development',
  level: 'info',
  message: 'E3 PDM System server running on port 3001',
  timestamp: '2025-07-24 23:56:06:566'
}
