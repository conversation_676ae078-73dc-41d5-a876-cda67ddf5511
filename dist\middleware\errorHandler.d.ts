import { Request, Response, NextFunction } from 'express';
import { AppError } from '../utils/errors';
export declare const errorHandler: (error: Error | AppError, req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, _res: Response, next: NextFunction) => void;
export declare const validationErrorHandler: (errors: any[]) => never;
export declare const handleDatabaseError: (error: any) => never;
export declare const handleFileError: (error: any, fileType?: string) => never;
//# sourceMappingURL=errorHandler.d.ts.map