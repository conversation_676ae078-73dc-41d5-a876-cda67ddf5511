{"ast": null, "code": "var _jsxFileName = \"D:\\\\GITHUB\\\\PDM\\\\frontend\\\\src\\\\components\\\\common\\\\ThemeToggle.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ThemeToggle = () => {\n  _s();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: toggleTheme,\n    className: \"theme-toggle btn btn-outline\",\n    title: `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,\n    \"aria-label\": `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,\n    children: theme === 'light' ?\n    /*#__PURE__*/\n    // Moon icon for dark mode\n    _jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    // Sun icon for light mode\n    _jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"Q4eAjrIZ0CuRuhycs6byifK2KBk=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "useTheme", "jsxDEV", "_jsxDEV", "ThemeToggle", "_s", "theme", "toggleTheme", "onClick", "className", "title", "children", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "_c", "$RefreshReg$"], "sources": ["D:/GITHUB/PDM/frontend/src/components/common/ThemeToggle.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nexport const ThemeToggle: React.FC = () => {\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className=\"theme-toggle btn btn-outline\"\n      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {theme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\" />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <circle cx=\"12\" cy=\"12\" r=\"5\" />\n          <path d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\" />\n        </svg>\n      )}\n    </button>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,OAAO,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGN,QAAQ,CAAC,CAAC;EAEzC,oBACEE,OAAA;IACEK,OAAO,EAAED,WAAY;IACrBE,SAAS,EAAC,8BAA8B;IACxCC,KAAK,EAAE,aAAaJ,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,OAAQ;IAChE,cAAY,aAAaA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,OAAQ;IAAAK,QAAA,EAEpEL,KAAK,KAAK,OAAO;IAAA;IAChB;IACAH,OAAA;MACES,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MACfC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC,OAAO;MAAAR,QAAA,eAEtBR,OAAA;QAAMiB,CAAC,EAAC;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;IAAA;IAEN;IACArB,OAAA;MACES,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MACfC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC,OAAO;MAAAR,QAAA,gBAEtBR,OAAA;QAAQsB,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC;MAAG;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChCrB,OAAA;QAAMiB,CAAC,EAAC;MAAoH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5H;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACnB,EAAA,CA1CWD,WAAqB;EAAA,QACDH,QAAQ;AAAA;AAAA2B,EAAA,GAD5BxB,WAAqB;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}