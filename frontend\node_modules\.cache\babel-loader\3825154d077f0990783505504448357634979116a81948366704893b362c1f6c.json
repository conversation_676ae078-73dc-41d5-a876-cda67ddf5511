{"ast": null, "code": "var _jsxFileName = \"D:\\\\GITHUB\\\\PDM\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LoginPage = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    login,\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n  const location = useLocation();\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n  useEffect(() => {\n    // Clear error when inputs change\n    if (error) {\n      setError('');\n    }\n  }, [username, password, error]);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  }\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: from,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 12\n    }, this);\n  }\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!username.trim() || !password.trim()) {\n      setError('Please enter both username and password');\n      return;\n    }\n    setIsSubmitting(true);\n    setError('');\n    try {\n      const success = await login(username.trim(), password);\n      if (!success) {\n        setError('Invalid username or password');\n      }\n    } catch (err) {\n      setError('An error occurred during login. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8 p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"E3 PDM System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-600\",\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"form-label\",\n              children: \"Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"username\",\n              name: \"username\",\n              type: \"text\",\n              required: true,\n              className: \"input\",\n              placeholder: \"Enter your username\",\n              value: username,\n              onChange: e => setUsername(e.target.value),\n              disabled: isSubmitting\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"form-label\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: \"password\",\n              required: true,\n              className: \"input\",\n              placeholder: \"Enter your password\",\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              disabled: isSubmitting\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-sm text-center bg-red-50 p-3 rounded-md\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isSubmitting,\n            className: \"btn btn-primary w-full flex justify-center items-center gap-2\",\n            children: [isSubmitting && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 32\n            }, this), isSubmitting ? 'Signing in...' : 'Sign in']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"2rpfzBZZaykuLViJTBEUYYTTfiU=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "_location$state", "_location$state$from", "username", "setUsername", "password", "setPassword", "error", "setError", "isSubmitting", "setIsSubmitting", "login", "isAuthenticated", "isLoading", "location", "from", "state", "pathname", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "handleSubmit", "e", "preventDefault", "trim", "success", "err", "onSubmit", "htmlFor", "id", "name", "type", "required", "placeholder", "value", "onChange", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/GITHUB/PDM/frontend/src/components/auth/LoginPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from './AuthContext';\n\nexport const LoginPage: React.FC = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const { login, isAuthenticated, isLoading } = useAuth();\n  const location = useLocation();\n\n  const from = location.state?.from?.pathname || '/';\n\n  useEffect(() => {\n    // Clear error when inputs change\n    if (error) {\n      setError('');\n    }\n  }, [username, password, error]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"spinner\"></div>\n      </div>\n    );\n  }\n\n  if (isAuthenticated) {\n    return <Navigate to={from} replace />;\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!username.trim() || !password.trim()) {\n      setError('Please enter both username and password');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setError('');\n\n    try {\n      const success = await login(username.trim(), password);\n      if (!success) {\n        setError('Invalid username or password');\n      }\n    } catch (err) {\n      setError('An error occurred during login. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900\">\n            E3 PDM System\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Sign in to your account\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"username\" className=\"form-label\">\n                Username\n              </label>\n              <input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                required\n                className=\"input\"\n                placeholder=\"Enter your username\"\n                value={username}\n                onChange={(e) => setUsername(e.target.value)}\n                disabled={isSubmitting}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"form-label\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                className=\"input\"\n                placeholder=\"Enter your password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                disabled={isSubmitting}\n              />\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"text-red-600 text-sm text-center bg-red-50 p-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"btn btn-primary w-full flex justify-center items-center gap-2\"\n            >\n              {isSubmitting && <div className=\"spinner\"></div>}\n              {isSubmitting ? 'Signing in...' : 'Sign in'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EACvC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAEmB,KAAK;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGjB,OAAO,CAAC,CAAC;EACvD,MAAMkB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,IAAI,GAAG,EAAAd,eAAA,GAAAa,QAAQ,CAACE,KAAK,cAAAf,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBc,IAAI,cAAAb,oBAAA,uBAApBA,oBAAA,CAAsBe,QAAQ,KAAI,GAAG;EAElDxB,SAAS,CAAC,MAAM;IACd;IACA,IAAIc,KAAK,EAAE;MACTC,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,QAAQ,EAAEE,KAAK,CAAC,CAAC;EAE/B,IAAIM,SAAS,EAAE;IACb,oBACEf,OAAA;MAAKoB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DrB,OAAA;QAAKoB,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAEV;EAEA,IAAIX,eAAe,EAAE;IACnB,oBAAOd,OAAA,CAACJ,QAAQ;MAAC8B,EAAE,EAAET,IAAK;MAACU,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvC;EAEA,MAAMG,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC,IAAI,CAACxB,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MACxCrB,QAAQ,CAAC,yCAAyC,CAAC;MACnD;IACF;IAEAE,eAAe,CAAC,IAAI,CAAC;IACrBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMsB,OAAO,GAAG,MAAMnB,KAAK,CAACR,QAAQ,CAAC0B,IAAI,CAAC,CAAC,EAAExB,QAAQ,CAAC;MACtD,IAAI,CAACyB,OAAO,EAAE;QACZtB,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZvB,QAAQ,CAAC,mDAAmD,CAAC;IAC/D,CAAC,SAAS;MACRE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKoB,SAAS,EAAC,0DAA0D;IAAAC,QAAA,eACvErB,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CrB,OAAA;QAAKoB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrB,OAAA;UAAIoB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzB,OAAA;UAAGoB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENzB,OAAA;QAAMoB,SAAS,EAAC,gBAAgB;QAACc,QAAQ,EAAEN,YAAa;QAAAP,QAAA,gBACtDrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAOmC,OAAO,EAAC,UAAU;cAACf,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzB,OAAA;cACEoC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfC,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRnB,SAAS,EAAC,OAAO;cACjBoB,WAAW,EAAC,qBAAqB;cACjCC,KAAK,EAAEpC,QAAS;cAChBqC,QAAQ,EAAGb,CAAC,IAAKvB,WAAW,CAACuB,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE;cAC7CG,QAAQ,EAAEjC;YAAa;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAOmC,OAAO,EAAC,UAAU;cAACf,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzB,OAAA;cACEoC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfC,IAAI,EAAC,UAAU;cACfC,QAAQ;cACRnB,SAAS,EAAC,OAAO;cACjBoB,WAAW,EAAC,qBAAqB;cACjCC,KAAK,EAAElC,QAAS;cAChBmC,QAAQ,EAAGb,CAAC,IAAKrB,WAAW,CAACqB,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE;cAC7CG,QAAQ,EAAEjC;YAAa;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhB,KAAK,iBACJT,OAAA;UAAKoB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EACvEZ;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDzB,OAAA;UAAAqB,QAAA,eACErB,OAAA;YACEsC,IAAI,EAAC,QAAQ;YACbM,QAAQ,EAAEjC,YAAa;YACvBS,SAAS,EAAC,+DAA+D;YAAAC,QAAA,GAExEV,YAAY,iBAAIX,OAAA;cAAKoB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC/Cd,YAAY,GAAG,eAAe,GAAG,SAAS;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA1HWD,SAAmB;EAAA,QAMgBH,OAAO,EACpCD,WAAW;AAAA;AAAAgD,EAAA,GAPjB5C,SAAmB;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}