"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processErrorWithAlerting = exports.alertManager = exports.AlertManager = void 0;
const logger_1 = __importDefault(require("./logger"));
const errors_1 = require("./errors");
const defaultAlertConfig = {
    enabled: process.env['NODE_ENV'] === 'production',
    criticalErrorCodes: [
        errors_1.ErrorCode.DATABASE_CONNECTION_FAILED,
        errors_1.ErrorCode.SERVICE_UNAVAILABLE,
        errors_1.ErrorCode.CONFIGURATION_ERROR,
        errors_1.ErrorCode.INTERNAL_SERVER_ERROR,
    ],
    alertThreshold: 5,
    timeWindow: 15,
    emailRecipients: process.env['ALERT_EMAIL_RECIPIENTS']?.split(',') || [],
    webhookUrl: process.env['ALERT_WEBHOOK_URL'],
    slackWebhookUrl: process.env['SLACK_WEBHOOK_URL'],
};
const errorTrackers = new Map();
class AlertManager {
    constructor(config = {}) {
        this.alertCooldowns = new Map();
        this.config = { ...defaultAlertConfig, ...config };
    }
    async processError(error, context) {
        if (!this.config.enabled || !this.shouldTrackError(error)) {
            return;
        }
        this.trackError(error, context);
        if (this.shouldTriggerAlert(error.code)) {
            await this.triggerAlert(error.code, context);
        }
    }
    shouldTrackError(error) {
        return (this.config.criticalErrorCodes.includes(error.code) ||
            error.statusCode >= 500);
    }
    trackError(error, context) {
        const now = new Date();
        const tracker = errorTrackers.get(error.code) || {
            count: 0,
            firstOccurrence: now,
            lastOccurrence: now,
            errors: [],
        };
        const timeWindowMs = this.config.timeWindow * 60 * 1000;
        tracker.errors = tracker.errors.filter((e) => now.getTime() - e.timestamp.getTime() < timeWindowMs);
        tracker.errors.push({
            timestamp: now,
            error,
            context,
        });
        tracker.count = tracker.errors.length;
        tracker.lastOccurrence = now;
        errorTrackers.set(error.code, tracker);
    }
    shouldTriggerAlert(errorCode) {
        const tracker = errorTrackers.get(errorCode);
        if (!tracker || tracker.count < this.config.alertThreshold) {
            return false;
        }
        const cooldown = this.alertCooldowns.get(errorCode);
        if (cooldown && new Date().getTime() - cooldown.getTime() < 30 * 60 * 1000) {
            return false;
        }
        return true;
    }
    async triggerAlert(errorCode, context) {
        const tracker = errorTrackers.get(errorCode);
        if (!tracker)
            return;
        const alertData = {
            errorCode,
            count: tracker.count,
            timeWindow: this.config.timeWindow,
            firstOccurrence: tracker.firstOccurrence,
            lastOccurrence: tracker.lastOccurrence,
            recentErrors: tracker.errors.slice(-3),
            context,
            timestamp: new Date().toISOString(),
        };
        logger_1.default.error('CRITICAL ALERT: Error threshold exceeded', {
            alert: alertData,
            system: 'E3-PDM-System',
        });
        this.alertCooldowns.set(errorCode, new Date());
        await Promise.allSettled([
            this.sendWebhookAlert(alertData),
            this.sendSlackAlert(alertData),
            this.sendEmailAlert(alertData),
        ]);
    }
    async sendWebhookAlert(alertData) {
        if (!this.config.webhookUrl)
            return;
        try {
            const response = await fetch(this.config.webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'critical_error_alert',
                    data: alertData,
                }),
            });
            if (!response.ok) {
                throw new Error(`Webhook alert failed: ${response.statusText}`);
            }
            logger_1.default.info('Webhook alert sent successfully', { errorCode: alertData.errorCode });
        }
        catch (error) {
            logger_1.default.error('Failed to send webhook alert', { error, alertData });
        }
    }
    async sendSlackAlert(alertData) {
        if (!this.config.slackWebhookUrl)
            return;
        try {
            const message = {
                text: `🚨 Critical Error Alert - E3 PDM System`,
                attachments: [
                    {
                        color: 'danger',
                        fields: [
                            {
                                title: 'Error Code',
                                value: alertData.errorCode,
                                short: true,
                            },
                            {
                                title: 'Occurrences',
                                value: `${alertData.count} in ${alertData.timeWindow} minutes`,
                                short: true,
                            },
                            {
                                title: 'First Occurrence',
                                value: alertData.firstOccurrence,
                                short: true,
                            },
                            {
                                title: 'Last Occurrence',
                                value: alertData.lastOccurrence,
                                short: true,
                            },
                        ],
                        footer: 'E3 PDM System Monitoring',
                        ts: Math.floor(Date.now() / 1000),
                    },
                ],
            };
            const response = await fetch(this.config.slackWebhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(message),
            });
            if (!response.ok) {
                throw new Error(`Slack alert failed: ${response.statusText}`);
            }
            logger_1.default.info('Slack alert sent successfully', { errorCode: alertData.errorCode });
        }
        catch (error) {
            logger_1.default.error('Failed to send Slack alert', { error, alertData });
        }
    }
    async sendEmailAlert(alertData) {
        if (!this.config.emailRecipients?.length)
            return;
        logger_1.default.info('Email alert would be sent', {
            recipients: this.config.emailRecipients,
            errorCode: alertData.errorCode,
            count: alertData.count,
        });
    }
    getErrorStatistics() {
        const stats = {};
        for (const [errorCode, tracker] of errorTrackers.entries()) {
            stats[errorCode] = {
                count: tracker.count,
                firstOccurrence: tracker.firstOccurrence,
                lastOccurrence: tracker.lastOccurrence,
                recentErrorsCount: tracker.errors.length,
            };
        }
        return stats;
    }
    clearErrorTracking(errorCode) {
        if (errorCode) {
            errorTrackers.delete(errorCode);
            this.alertCooldowns.delete(errorCode);
        }
        else {
            errorTrackers.clear();
            this.alertCooldowns.clear();
        }
    }
}
exports.AlertManager = AlertManager;
exports.alertManager = new AlertManager();
const processErrorWithAlerting = async (error, context) => {
    await exports.alertManager.processError(error, context);
};
exports.processErrorWithAlerting = processErrorWithAlerting;
//# sourceMappingURL=alerting.js.map