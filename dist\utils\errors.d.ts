export declare enum ErrorCode {
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED",
    AUTHORIZATION_FAILED = "AUTHORIZATION_FAILED",
    TOKEN_EXPIRED = "TOKEN_EXPIRED",
    INVALID_TOKEN = "INVALID_TOKEN",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    INVALID_INPUT = "INVALID_INPUT",
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD",
    FILE_NOT_FOUND = "FILE_NOT_FOUND",
    FILE_UPLOAD_FAILED = "FILE_UPLOAD_FAILED",
    FILE_PROCESSING_FAILED = "FILE_PROCESSING_FAILED",
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT",
    FILE_SIZE_EXCEEDED = "FILE_SIZE_EXCEEDED",
    FILE_CORRUPTED = "FILE_CORRUPTED",
    UNSUPPORTED_E3_VERSION = "UNSUPPORTED_E3_VERSION",
    PROJECT_NOT_FOUND = "PROJECT_NOT_FOUND",
    PROJECT_ACCESS_DENIED = "PROJECT_ACCESS_DENIED",
    PROJECT_CREATION_FAILED = "PROJECT_CREATION_FAILED",
    PROJECT_UPDATE_FAILED = "PROJECT_UPDATE_FAILED",
    PROJECT_DELETION_FAILED = "PROJECT_DELETION_FAILED",
    VERSION_NOT_FOUND = "VERSION_NOT_FOUND",
    VERSION_LOCKED = "VERSION_LOCKED",
    VERSION_CONFLICT = "VERSION_CONFLICT",
    VERSION_CREATION_FAILED = "VERSION_CREATION_FAILED",
    DATABASE_CONNECTION_FAILED = "DATABASE_CONNECTION_FAILED",
    DATABASE_QUERY_FAILED = "DATABASE_QUERY_FAILED",
    DATABASE_CONSTRAINT_VIOLATION = "DATABASE_CONSTRAINT_VIOLATION",
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
}
export declare class AppError extends Error {
    readonly code: ErrorCode;
    readonly statusCode: number;
    readonly isOperational: boolean;
    readonly details: Record<string, any> | undefined;
    readonly userMessage: string;
    readonly timestamp: string;
    constructor(code: ErrorCode, message: string, statusCode?: number, userMessage?: string, details?: Record<string, any>, isOperational?: boolean);
    private getDefaultStatusCode;
    private getDefaultUserMessage;
    toJSON(): {
        error: {
            code: ErrorCode;
            message: string;
            details: Record<string, any> | undefined;
            timestamp: string;
        };
    };
}
export declare class ValidationError extends AppError {
    constructor(message: string, details?: Record<string, any>);
}
export declare class AuthenticationError extends AppError {
    constructor(message?: string);
}
export declare class AuthorizationError extends AppError {
    constructor(message?: string);
}
export declare class NotFoundError extends AppError {
    constructor(resource: string, id?: string);
}
export declare class FileProcessingError extends AppError {
    constructor(message: string, code?: ErrorCode, details?: Record<string, any>);
}
export declare class DatabaseError extends AppError {
    constructor(message: string, originalError?: Error);
}
export declare const getRecoverySuggestions: (code: ErrorCode) => string[];
//# sourceMappingURL=errors.d.ts.map