#!/usr/bin/env node

/**
 * Database initialization script
 * This script initializes the database schema and runs migrations
 */

import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import { DatabaseConnection, dbConfig } from './connection';
import { createMigrationManager } from './migrations';

async function initializeDatabase() {
  console.log('🚀 Starting database initialization...');
  
  try {
    // Initialize database connection
    const db = DatabaseConnection.getInstance(dbConfig);
    
    // Test connection
    console.log('📡 Testing database connection...');
    const isConnected = await db.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }
    console.log('✅ Database connection successful');
    
    // Initialize migration manager
    const migrationManager = createMigrationManager(db);
    
    // Check migration status
    console.log('📋 Checking migration status...');
    const status = await migrationManager.getStatus();
    console.log(`Applied migrations: ${status.applied.length}`);
    console.log(`Pending migrations: ${status.pending.length}`);
    
    // Run migrations if needed
    if (status.pending.length > 0) {
      console.log('🔄 Running pending migrations...');
      await migrationManager.migrate();
      console.log('✅ All migrations applied successfully');
    } else {
      console.log('✅ Database is up to date');
    }
    
    // Display final status
    const finalStatus = await migrationManager.getStatus();
    console.log('\n📊 Database Status:');
    console.log(`- Total applied migrations: ${finalStatus.applied.length}`);
    console.log(`- Pending migrations: ${finalStatus.pending.length}`);
    
    if (finalStatus.applied.length > 0) {
      console.log('\n📝 Applied migrations:');
      finalStatus.applied.forEach(migration => {
        console.log(`  - ${migration.id}: ${migration.name} (${migration.appliedAt?.toISOString()})`);
      });
    }
    
    // Close connection
    await db.close();
    console.log('\n🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase();
}

export { initializeDatabase };