{"ast": null, "code": "import { apiClient } from './apiClient';\nclass AuthService {\n  constructor() {\n    this.tokenKey = 'pdm_auth_token';\n    this.refreshTokenKey = 'pdm_refresh_token';\n  }\n  async login(username, password) {\n    const credentials = {\n      username,\n      password\n    };\n    const response = await apiClient.post('/auth/login', credentials, {\n      skipAuth: true\n    });\n    if (!response.success) {\n      throw new Error(response.error || 'Login failed');\n    }\n    return response.data;\n  }\n  async getCurrentUser() {\n    const token = this.getStoredToken();\n    if (!token) {\n      throw new Error('No authentication token found');\n    }\n    const response = await apiClient.get('/auth/me');\n    if (!response.success) {\n      throw new Error(response.error || 'Failed to get user info');\n    }\n    return response.data;\n  }\n  async refreshToken() {\n    const token = this.getStoredToken();\n    if (!(token !== null && token !== void 0 && token.token)) {\n      return null;\n    }\n    try {\n      const response = await apiClient.post('/auth/refresh', {\n        token: token.token\n      }, {\n        skipAuth: true\n      });\n      if (response.success && response.data) {\n        const newToken = response.data;\n        this.setToken(newToken);\n        return newToken;\n      }\n      return null;\n    } catch (error) {\n      this.removeToken();\n      return null;\n    }\n  }\n  setToken(token) {\n    localStorage.setItem(this.tokenKey, token.token);\n    localStorage.setItem(this.refreshTokenKey, token.refreshToken);\n  }\n  getStoredToken() {\n    const token = localStorage.getItem(this.tokenKey);\n    const refreshToken = localStorage.getItem(this.refreshTokenKey);\n    if (!token || !refreshToken) {\n      return null;\n    }\n    return {\n      token,\n      refreshToken,\n      expiresAt: '' // We'll handle expiration through API responses\n    };\n  }\n  removeToken() {\n    localStorage.removeItem(this.tokenKey);\n    localStorage.removeItem(this.refreshTokenKey);\n  }\n  isTokenExpired(token) {\n    if (!token.expiresAt) return false;\n    const expirationTime = new Date(token.expiresAt).getTime();\n    const currentTime = new Date().getTime();\n    return currentTime >= expirationTime;\n  }\n  async logout() {\n    const token = this.getStoredToken();\n    if (token) {\n      try {\n        await apiClient.post('/auth/logout');\n      } catch (error) {\n        // Ignore logout errors, just remove local token\n        console.warn('Logout request failed:', error);\n      }\n    }\n    this.removeToken();\n  }\n}\nexport const authService = new AuthService();", "map": {"version": 3, "names": ["apiClient", "AuthService", "constructor", "<PERSON><PERSON><PERSON>", "refreshT<PERSON><PERSON><PERSON>", "login", "username", "password", "credentials", "response", "post", "<PERSON><PERSON><PERSON>", "success", "Error", "error", "data", "getCurrentUser", "token", "getStoredToken", "get", "refreshToken", "newToken", "setToken", "removeToken", "localStorage", "setItem", "getItem", "expiresAt", "removeItem", "isTokenExpired", "expirationTime", "Date", "getTime", "currentTime", "logout", "console", "warn", "authService"], "sources": ["D:/GITHUB/PDM/frontend/src/services/authService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\nimport { User, AuthToken, LoginCredentials, AuthUser } from '../types';\n\nclass AuthService {\n  private tokenKey = 'pdm_auth_token';\n  private refreshTokenKey = 'pdm_refresh_token';\n\n  async login(username: string, password: string): Promise<AuthUser> {\n    const credentials: LoginCredentials = { username, password };\n    \n    const response = await apiClient.post<AuthUser>('/auth/login', credentials, { skipAuth: true });\n    \n    if (!response.success) {\n      throw new Error(response.error || 'Login failed');\n    }\n\n    return response.data!;\n  }\n\n  async getCurrentUser(): Promise<User> {\n    const token = this.getStoredToken();\n    if (!token) {\n      throw new Error('No authentication token found');\n    }\n\n    const response = await apiClient.get<User>('/auth/me');\n\n    if (!response.success) {\n      throw new Error(response.error || 'Failed to get user info');\n    }\n\n    return response.data!;\n  }\n\n  async refreshToken(): Promise<AuthToken | null> {\n    const token = this.getStoredToken();\n    if (!token?.token) {\n      return null;\n    }\n\n    try {\n      const response = await apiClient.post<AuthToken>('/auth/refresh', {\n        token: token.token,\n      }, { skipAuth: true });\n\n      if (response.success && response.data) {\n        const newToken = response.data;\n        this.setToken(newToken);\n        return newToken;\n      }\n      return null;\n    } catch (error) {\n      this.removeToken();\n      return null;\n    }\n  }\n\n  setToken(token: AuthToken): void {\n    localStorage.setItem(this.tokenKey, token.token);\n    localStorage.setItem(this.refreshTokenKey, token.refreshToken);\n  }\n\n  getStoredToken(): AuthToken | null {\n    const token = localStorage.getItem(this.tokenKey);\n    const refreshToken = localStorage.getItem(this.refreshTokenKey);\n    \n    if (!token || !refreshToken) {\n      return null;\n    }\n\n    return {\n      token,\n      refreshToken,\n      expiresAt: '', // We'll handle expiration through API responses\n    };\n  }\n\n  removeToken(): void {\n    localStorage.removeItem(this.tokenKey);\n    localStorage.removeItem(this.refreshTokenKey);\n  }\n\n  isTokenExpired(token: AuthToken): boolean {\n    if (!token.expiresAt) return false;\n    \n    const expirationTime = new Date(token.expiresAt).getTime();\n    const currentTime = new Date().getTime();\n    \n    return currentTime >= expirationTime;\n  }\n\n  async logout(): Promise<void> {\n    const token = this.getStoredToken();\n    \n    if (token) {\n      try {\n        await apiClient.post('/auth/logout');\n      } catch (error) {\n        // Ignore logout errors, just remove local token\n        console.warn('Logout request failed:', error);\n      }\n    }\n    \n    this.removeToken();\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAGvC,MAAMC,WAAW,CAAC;EAAAC,YAAA;IAAA,KACRC,QAAQ,GAAG,gBAAgB;IAAA,KAC3BC,eAAe,GAAG,mBAAmB;EAAA;EAE7C,MAAMC,KAAKA,CAACC,QAAgB,EAAEC,QAAgB,EAAqB;IACjE,MAAMC,WAA6B,GAAG;MAAEF,QAAQ;MAAEC;IAAS,CAAC;IAE5D,MAAME,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAW,aAAa,EAAEF,WAAW,EAAE;MAAEG,QAAQ,EAAE;IAAK,CAAC,CAAC;IAE/F,IAAI,CAACF,QAAQ,CAACG,OAAO,EAAE;MACrB,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,KAAK,IAAI,cAAc,CAAC;IACnD;IAEA,OAAOL,QAAQ,CAACM,IAAI;EACtB;EAEA,MAAMC,cAAcA,CAAA,EAAkB;IACpC,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIJ,KAAK,CAAC,+BAA+B,CAAC;IAClD;IAEA,MAAMJ,QAAQ,GAAG,MAAMT,SAAS,CAACmB,GAAG,CAAO,UAAU,CAAC;IAEtD,IAAI,CAACV,QAAQ,CAACG,OAAO,EAAE;MACrB,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,KAAK,IAAI,yBAAyB,CAAC;IAC9D;IAEA,OAAOL,QAAQ,CAACM,IAAI;EACtB;EAEA,MAAMK,YAAYA,CAAA,EAA8B;IAC9C,MAAMH,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,EAACD,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEA,KAAK,GAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMT,SAAS,CAACU,IAAI,CAAY,eAAe,EAAE;QAChEO,KAAK,EAAEA,KAAK,CAACA;MACf,CAAC,EAAE;QAAEN,QAAQ,EAAE;MAAK,CAAC,CAAC;MAEtB,IAAIF,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACM,IAAI,EAAE;QACrC,MAAMM,QAAQ,GAAGZ,QAAQ,CAACM,IAAI;QAC9B,IAAI,CAACO,QAAQ,CAACD,QAAQ,CAAC;QACvB,OAAOA,QAAQ;MACjB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,IAAI,CAACS,WAAW,CAAC,CAAC;MAClB,OAAO,IAAI;IACb;EACF;EAEAD,QAAQA,CAACL,KAAgB,EAAQ;IAC/BO,YAAY,CAACC,OAAO,CAAC,IAAI,CAACtB,QAAQ,EAAEc,KAAK,CAACA,KAAK,CAAC;IAChDO,YAAY,CAACC,OAAO,CAAC,IAAI,CAACrB,eAAe,EAAEa,KAAK,CAACG,YAAY,CAAC;EAChE;EAEAF,cAAcA,CAAA,EAAqB;IACjC,MAAMD,KAAK,GAAGO,YAAY,CAACE,OAAO,CAAC,IAAI,CAACvB,QAAQ,CAAC;IACjD,MAAMiB,YAAY,GAAGI,YAAY,CAACE,OAAO,CAAC,IAAI,CAACtB,eAAe,CAAC;IAE/D,IAAI,CAACa,KAAK,IAAI,CAACG,YAAY,EAAE;MAC3B,OAAO,IAAI;IACb;IAEA,OAAO;MACLH,KAAK;MACLG,YAAY;MACZO,SAAS,EAAE,EAAE,CAAE;IACjB,CAAC;EACH;EAEAJ,WAAWA,CAAA,EAAS;IAClBC,YAAY,CAACI,UAAU,CAAC,IAAI,CAACzB,QAAQ,CAAC;IACtCqB,YAAY,CAACI,UAAU,CAAC,IAAI,CAACxB,eAAe,CAAC;EAC/C;EAEAyB,cAAcA,CAACZ,KAAgB,EAAW;IACxC,IAAI,CAACA,KAAK,CAACU,SAAS,EAAE,OAAO,KAAK;IAElC,MAAMG,cAAc,GAAG,IAAIC,IAAI,CAACd,KAAK,CAACU,SAAS,CAAC,CAACK,OAAO,CAAC,CAAC;IAC1D,MAAMC,WAAW,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAExC,OAAOC,WAAW,IAAIH,cAAc;EACtC;EAEA,MAAMI,MAAMA,CAAA,EAAkB;IAC5B,MAAMjB,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAEnC,IAAID,KAAK,EAAE;MACT,IAAI;QACF,MAAMjB,SAAS,CAACU,IAAI,CAAC,cAAc,CAAC;MACtC,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd;QACAqB,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEtB,KAAK,CAAC;MAC/C;IACF;IAEA,IAAI,CAACS,WAAW,CAAC,CAAC;EACpB;AACF;AAEA,OAAO,MAAMc,WAAW,GAAG,IAAIpC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}