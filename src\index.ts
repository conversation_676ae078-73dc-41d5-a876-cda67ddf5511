import * as dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { getDatabase, dbConfig } from './database';
import { createAuthRoutes, createProjectRoutes, createFileRoutes, createVersionRoutes, createDashboardRoutes } from './routes';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import logger, { morganStream } from './utils/logger';
import { AppError, ErrorCode } from './utils/errors';
import { processErrorWithAlerting } from './utils/alerting';

const app = express();
const PORT = process.env['PORT'] || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined', { stream: morganStream }));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Initialize database and routes
async function initializeApp() {
  try {
    const dbConnection = getDatabase(dbConfig);
    const db = dbConnection.getPool();

    // API routes
    app.use('/api/auth', createAuthRoutes(db));
    app.use('/api/projects', createProjectRoutes(db));
    app.use('/api/files', createFileRoutes(db));
    app.use('/api/versions', createVersionRoutes(db));
    app.use('/api/dashboard', createDashboardRoutes(db));

    logger.info('API routes initialized successfully');
  } catch (error) {
    const appError = new AppError(
      ErrorCode.CONFIGURATION_ERROR,
      'Failed to initialize application',
      500,
      'System initialization failed'
    );
    
    logger.error('Failed to initialize database and routes', { error, stack: error instanceof Error ? error.stack : undefined });
    await processErrorWithAlerting(appError, { initializationError: error });
    process.exit(1);
  }
}

// Initialize the app
initializeApp();

// 404 handler for unmatched routes
app.use('*', notFoundHandler);

// Centralized error handling middleware (must be last)
app.use(errorHandler);

app.listen(PORT, () => {
  logger.info(`E3 PDM System server running on port ${PORT}`, {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
  });
});

export default app;