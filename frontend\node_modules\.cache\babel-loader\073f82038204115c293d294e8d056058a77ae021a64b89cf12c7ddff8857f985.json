{"ast": null, "code": "var _jsxFileName = \"D:\\\\GITHUB\\\\PDM\\\\frontend\\\\src\\\\components\\\\common\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Layout = ({\n  children\n}) => {\n  _s();\n  var _user$username;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const location = useLocation();\n  const navigation = [{\n    name: 'Dashboard',\n    href: '/',\n    icon: '🏠'\n  }, {\n    name: 'Projects',\n    href: '/projects',\n    icon: '📁'\n  }, {\n    name: 'Files',\n    href: '/files',\n    icon: '📄'\n  }, {\n    name: 'Settings',\n    href: '/settings',\n    icon: '⚙️'\n  }];\n  const isActive = href => {\n    if (href === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(href);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold text-primary\",\n          children: \"E3 PDM System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav-menu\",\n          children: navigation.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: item.href,\n              className: `nav-link ${isActive(item.href) ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"nav-icon\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this), item.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this)\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-auto pt-6 border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium\",\n            children: user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900 truncate\",\n              children: user === null || user === void 0 ? void 0 : user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 truncate\",\n              children: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"btn btn-outline w-full text-sm\",\n          children: \"Sign out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"1YtijdKLJZf6h0pg3ieL7Q2hlX8=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "_user$username", "user", "logout", "location", "navigation", "name", "href", "icon", "isActive", "pathname", "startsWith", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "username", "char<PERSON>t", "toUpperCase", "role", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/GITHUB/PDM/frontend/src/components/common/Layout.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthContext';\nimport { ThemeToggle } from './ThemeToggle';\n\ninterface LayoutProps {\n  children: ReactNode;\n}\n\nexport const Layout: React.FC<LayoutProps> = ({ children }) => {\n  const { user, logout } = useAuth();\n  const location = useLocation();\n\n  const navigation = [\n    { name: 'Dashboard', href: '/', icon: '🏠' },\n    { name: 'Projects', href: '/projects', icon: '📁' },\n    { name: 'Files', href: '/files', icon: '📄' },\n    { name: 'Settings', href: '/settings', icon: '⚙️' },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(href);\n  };\n\n  return (\n    <div className=\"layout\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-xl font-bold text-primary\">E3 PDM System</h1>\n        </div>\n        \n        <nav>\n          <ul className=\"nav-menu\">\n            {navigation.map((item) => (\n              <li key={item.name} className=\"nav-item\">\n                <Link\n                  to={item.href}\n                  className={`nav-link ${isActive(item.href) ? 'active' : ''}`}\n                >\n                  <span className=\"nav-icon\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        {/* User info and logout */}\n        <div className=\"mt-auto pt-6 border-t border-gray-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium\">\n              {user?.username?.charAt(0).toUpperCase()}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">\n                {user?.username}\n              </p>\n              <p className=\"text-xs text-gray-500 truncate\">\n                {user?.role}\n              </p>\n            </div>\n          </div>\n          <button\n            onClick={logout}\n            className=\"btn btn-outline w-full text-sm\"\n          >\n            Sign out\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"main-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO9C,OAAO,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC7D,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC5C;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC7C;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,CACpD;EAED,MAAMC,QAAQ,GAAIF,IAAY,IAAK;IACjC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,OAAOH,QAAQ,CAACM,QAAQ,KAAK,GAAG;IAClC;IACA,OAAON,QAAQ,CAACM,QAAQ,CAACC,UAAU,CAACJ,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEV,OAAA;IAAKe,SAAS,EAAC,QAAQ;IAAAb,QAAA,gBAErBF,OAAA;MAAKe,SAAS,EAAC,SAAS;MAAAb,QAAA,gBACtBF,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAb,QAAA,eACnBF,OAAA;UAAIe,SAAS,EAAC,gCAAgC;UAAAb,QAAA,EAAC;QAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAENnB,OAAA;QAAAE,QAAA,eACEF,OAAA;UAAIe,SAAS,EAAC,UAAU;UAAAb,QAAA,EACrBM,UAAU,CAACY,GAAG,CAAEC,IAAI,iBACnBrB,OAAA;YAAoBe,SAAS,EAAC,UAAU;YAAAb,QAAA,eACtCF,OAAA,CAACJ,IAAI;cACH0B,EAAE,EAAED,IAAI,CAACX,IAAK;cACdK,SAAS,EAAE,YAAYH,QAAQ,CAACS,IAAI,CAACX,IAAI,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAR,QAAA,gBAE7DF,OAAA;gBAAMe,SAAS,EAAC,UAAU;gBAAAb,QAAA,EAAEmB,IAAI,CAACV;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC5CE,IAAI,CAACZ,IAAI;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC,GAPAE,IAAI,CAACZ,IAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNnB,OAAA;QAAKe,SAAS,EAAC,uCAAuC;QAAAb,QAAA,gBACpDF,OAAA;UAAKe,SAAS,EAAC,8BAA8B;UAAAb,QAAA,gBAC3CF,OAAA;YAAKe,SAAS,EAAC,iGAAiG;YAAAb,QAAA,EAC7GG,IAAI,aAAJA,IAAI,wBAAAD,cAAA,GAAJC,IAAI,CAAEkB,QAAQ,cAAAnB,cAAA,uBAAdA,cAAA,CAAgBoB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNnB,OAAA;YAAKe,SAAS,EAAC,gBAAgB;YAAAb,QAAA,gBAC7BF,OAAA;cAAGe,SAAS,EAAC,4CAA4C;cAAAb,QAAA,EACtDG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACJnB,OAAA;cAAGe,SAAS,EAAC,gCAAgC;cAAAb,QAAA,EAC1CG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnB,OAAA;UACE2B,OAAO,EAAErB,MAAO;UAChBS,SAAS,EAAC,gCAAgC;UAAAb,QAAA,EAC3C;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnB,OAAA;MAAKe,SAAS,EAAC,cAAc;MAAAb,QAAA,EAC1BA;IAAQ;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAxEWF,MAA6B;EAAA,QACfH,OAAO,EACfD,WAAW;AAAA;AAAA+B,EAAA,GAFjB3B,MAA6B;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}