{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": "AAKA,oBAAY,SAAS;IAEnB,qBAAqB,0BAA0B;IAC/C,oBAAoB,yBAAyB;IAC7C,aAAa,kBAAkB;IAC/B,aAAa,kBAAkB;IAG/B,gBAAgB,qBAAqB;IACrC,aAAa,kBAAkB;IAC/B,sBAAsB,2BAA2B;IAGjD,cAAc,mBAAmB;IACjC,kBAAkB,uBAAuB;IACzC,sBAAsB,2BAA2B;IACjD,mBAAmB,wBAAwB;IAC3C,kBAAkB,uBAAuB;IACzC,cAAc,mBAAmB;IACjC,sBAAsB,2BAA2B;IAGjD,iBAAiB,sBAAsB;IACvC,qBAAqB,0BAA0B;IAC/C,uBAAuB,4BAA4B;IACnD,qBAAqB,0BAA0B;IAC/C,uBAAuB,4BAA4B;IAGnD,iBAAiB,sBAAsB;IACvC,cAAc,mBAAmB;IACjC,gBAAgB,qBAAqB;IACrC,uBAAuB,4BAA4B;IAGnD,0BAA0B,+BAA+B;IACzD,qBAAqB,0BAA0B;IAC/C,6BAA6B,kCAAkC;IAG/D,qBAAqB,0BAA0B;IAC/C,mBAAmB,wBAAwB;IAC3C,mBAAmB,wBAAwB;CAC5C;AAED,qBAAa,QAAS,SAAQ,KAAK;IACjC,SAAgB,IAAI,EAAE,SAAS,CAAC;IAChC,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,aAAa,EAAE,OAAO,CAAC;IACvC,SAAgB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;IACzD,SAAgB,WAAW,EAAE,MAAM,CAAC;IACpC,SAAgB,SAAS,EAAE,MAAM,CAAC;gBAGhC,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,UAAU,CAAC,EAAE,MAAM,EACnB,WAAW,CAAC,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAC7B,aAAa,GAAE,OAAc;IAe/B,OAAO,CAAC,oBAAoB;IAiD5B,OAAO,CAAC,qBAAqB;IA0CtB,MAAM;;;;;;;;CAUd;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAG3D;AAED,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAgC;CAGtD;AAED,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAwB;CAG9C;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM;CAM1C;AAED,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,EAAE,MAAM,EAAE,IAAI,GAAE,SAA4C,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAG/G;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,KAAK;CASnD;AAGD,eAAO,MAAM,sBAAsB,GAAI,MAAM,SAAS,KAAG,MAAM,EAoC9D,CAAC"}