export interface Project {
    id: string;
    name: string;
    description: string;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
    status: 'active' | 'archived';
    metadata: Record<string, any>;
    permissions: ProjectPermission[];
}
export interface ProjectPermission {
    userId: string;
    role: 'admin' | 'editor' | 'viewer';
    grantedBy: string;
    grantedAt: Date;
}
export interface FileRecord {
    id: string;
    projectId: string;
    name: string;
    originalName: string;
    fileType: 'e3series' | 'pdf' | 'dxf';
    size: number;
    checksum: string;
    uploadedBy: string;
    uploadedAt: Date;
    currentVersion: string;
    metadata: FileMetadata;
    versions: Version[];
}
export interface Version {
    id: string;
    fileId: string;
    version: string;
    createdBy: string;
    createdAt: Date;
    changes: string;
    isLocked: boolean;
    isReleased: boolean;
    filePath: string;
}
export interface FileMetadata {
    title?: string;
    author?: string;
    createdDate?: Date;
    pageCount?: number;
    layers?: string[];
    dimensions?: {
        width: number;
        height: number;
    };
    [key: string]: any;
}
export interface E3ProjectData {
    components: Component[];
    connections: Connection[];
    properties: Record<string, any>;
    layers: Layer[];
    sheets: Sheet[];
}
export interface Component {
    id: string;
    name: string;
    type: string;
    properties: Record<string, any>;
    position: {
        x: number;
        y: number;
    };
    connections: string[];
}
export interface Connection {
    id: string;
    fromComponent: string;
    toComponent: string;
    signal: string;
    properties: Record<string, any>;
}
export interface Layer {
    id: string;
    name: string;
    visible: boolean;
    color: string;
}
export interface Sheet {
    id: string;
    name: string;
    size: string;
    components: string[];
}
export interface User {
    id: string;
    username: string;
    email: string;
    role: 'admin' | 'user';
    createdAt: Date;
    lastLogin?: Date;
}
export interface PDFMetadata {
    title?: string;
    author?: string;
    subject?: string;
    creator?: string;
    producer?: string;
    creationDate?: Date;
    modificationDate?: Date;
    pageCount: number;
}
export interface DXFMetadata {
    version: string;
    layers: string[];
    blocks: string[];
    dimensions: {
        width: number;
        height: number;
    };
    units: string;
}
export interface FileValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
export interface ErrorResponse {
    error: {
        code: string;
        message: string;
        details?: Record<string, any>;
        timestamp: string;
    };
}
export interface FileFilters {
    fileType?: 'e3series' | 'pdf' | 'dxf';
    dateFrom?: Date;
    dateTo?: Date;
    author?: string;
    version?: string;
}
export interface ProjectData {
    name: string;
    description: string;
    metadata?: Record<string, any>;
}
export interface VersionChanges {
    description: string;
    modifiedBy: string;
}
export interface VersionDiff {
    added: string[];
    removed: string[];
    modified: string[];
    details: Record<string, any>;
}
export interface AuditTrailEntry {
    id: string;
    tableName: string;
    recordId: string;
    action: 'INSERT' | 'UPDATE' | 'DELETE';
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    changedBy?: string;
    changedAt: Date;
    ipAddress?: string;
    userAgent?: string;
}
export interface AuditTrailQuery {
    tableName?: string;
    recordId?: string;
    action?: 'INSERT' | 'UPDATE' | 'DELETE';
    changedBy?: string;
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
}
export interface AuditTrailSummary {
    totalChanges: number;
    changesByAction: Record<string, number>;
    changesByUser: Record<string, number>;
    recentChanges: AuditTrailEntry[];
}
export interface ChangeHistoryEntry {
    id: string;
    entityType: 'project' | 'file' | 'version';
    entityId: string;
    entityName: string;
    action: string;
    description: string;
    changedBy: string;
    changedByName?: string;
    changedAt: Date;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    ipAddress?: string;
}
//# sourceMappingURL=index.d.ts.map