"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
class AuthenticationService {
    constructor(db, jwtSecret, jwtExpiresIn = '1h') {
        this.refreshTokens = new Set();
        this.db = db;
        this.jwtSecret = jwtSecret;
        this.jwtExpiresIn = jwtExpiresIn;
    }
    async authenticate(username, password) {
        const client = await this.db.connect();
        try {
            const userQuery = `
        SELECT id, username, email, password_hash, role, created_at, last_login, is_active
        FROM users 
        WHERE (username = $1 OR email = $1) AND is_active = true
      `;
            const result = await client.query(userQuery, [username]);
            if (result.rows.length === 0) {
                throw new Error('Invalid credentials');
            }
            const userData = result.rows[0];
            const isValidPassword = await bcryptjs_1.default.compare(password, userData.password_hash);
            if (!isValidPassword) {
                throw new Error('Invalid credentials');
            }
            await client.query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1', [userData.id]);
            const user = {
                id: userData.id,
                username: userData.username,
                email: userData.email,
                role: userData.role,
                createdAt: userData.created_at,
                lastLogin: new Date()
            };
            const token = this.generateToken(user);
            this.refreshTokens.add(token);
            return { user, token };
        }
        finally {
            client.release();
        }
    }
    async validateToken(token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, this.jwtSecret);
            const client = await this.db.connect();
            try {
                const userQuery = `
          SELECT id, username, email, role, created_at, last_login
          FROM users 
          WHERE id = $1 AND is_active = true
        `;
                const result = await client.query(userQuery, [decoded.userId]);
                if (result.rows.length === 0) {
                    throw new Error('User not found or inactive');
                }
                const userData = result.rows[0];
                return {
                    id: userData.id,
                    username: userData.username,
                    email: userData.email,
                    role: userData.role,
                    createdAt: userData.created_at,
                    lastLogin: userData.last_login
                };
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            throw new Error('Invalid or expired token');
        }
    }
    async refreshToken(token) {
        try {
            const user = await this.validateToken(token);
            this.refreshTokens.delete(token);
            const newToken = this.generateToken(user);
            this.refreshTokens.add(newToken);
            return newToken;
        }
        catch (error) {
            throw new Error('Unable to refresh token');
        }
    }
    async logout(token) {
        this.refreshTokens.delete(token);
    }
    async createUser(username, email, password, role = 'user') {
        const client = await this.db.connect();
        try {
            const saltRounds = 12;
            const passwordHash = await bcryptjs_1.default.hash(password, saltRounds);
            const insertQuery = `
        INSERT INTO users (username, email, password_hash, role)
        VALUES ($1, $2, $3, $4)
        RETURNING id, username, email, role, created_at
      `;
            const result = await client.query(insertQuery, [username, email, passwordHash, role]);
            const userData = result.rows[0];
            return {
                id: userData.id,
                username: userData.username,
                email: userData.email,
                role: userData.role,
                createdAt: userData.created_at
            };
        }
        catch (error) {
            if (error.code === '23505') {
                if (error.constraint?.includes('username')) {
                    throw new Error('Username already exists');
                }
                else if (error.constraint?.includes('email')) {
                    throw new Error('Email already exists');
                }
            }
            throw error;
        }
        finally {
            client.release();
        }
    }
    async changePassword(userId, currentPassword, newPassword) {
        const client = await this.db.connect();
        try {
            const userQuery = 'SELECT password_hash FROM users WHERE id = $1 AND is_active = true';
            const result = await client.query(userQuery, [userId]);
            if (result.rows.length === 0) {
                throw new Error('User not found');
            }
            const isValidPassword = await bcryptjs_1.default.compare(currentPassword, result.rows[0].password_hash);
            if (!isValidPassword) {
                throw new Error('Current password is incorrect');
            }
            const saltRounds = 12;
            const newPasswordHash = await bcryptjs_1.default.hash(newPassword, saltRounds);
            await client.query('UPDATE users SET password_hash = $1 WHERE id = $2', [newPasswordHash, userId]);
        }
        finally {
            client.release();
        }
    }
    generateToken(user) {
        const payload = {
            userId: user.id,
            username: user.username,
            email: user.email,
            role: user.role
        };
        return jsonwebtoken_1.default.sign(payload, this.jwtSecret, {
            expiresIn: this.jwtExpiresIn,
            issuer: 'e3-pdm-system',
            audience: 'e3-pdm-users'
        });
    }
}
exports.AuthenticationService = AuthenticationService;
//# sourceMappingURL=AuthenticationService.js.map