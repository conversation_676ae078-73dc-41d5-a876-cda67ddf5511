<!DOCTYPE html>
<html>
<head>
    <title>Dashboard Test</title>
</head>
<body>
    <h1>Dashboard API Test</h1>
    
    <div id="login-section">
        <h2>Login</h2>
        <input type="text" id="username" placeholder="Username" value="testadmin">
        <input type="password" id="password" placeholder="Password" value="">
        <button onclick="login()">Login</button>
    </div>
    
    <div id="dashboard-section" style="display:none;">
        <h2>Dashboard Stats</h2>
        <button onclick="getDashboardStats()">Get Dashboard Stats</button>
        <div id="stats-result"></div>
    </div>
    
    <div id="result"></div>

    <script>
        let authToken = null;

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    authToken = data.token;
                    document.getElementById('result').innerHTML = `<p style="color: green;">Login successful!</p>`;
                    document.getElementById('login-section').style.display = 'none';
                    document.getElementById('dashboard-section').style.display = 'block';
                } else {
                    document.getElementById('result').innerHTML = `<p style="color: red;">Login failed: ${data.error || 'Unknown error'}</p>`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }

        async function getDashboardStats() {
            if (!authToken) {
                document.getElementById('stats-result').innerHTML = '<p style="color: red;">Not logged in</p>';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:3001/api/dashboard/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('stats-result').innerHTML = `
                        <p style="color: green;">Dashboard stats retrieved successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    document.getElementById('stats-result').innerHTML = `<p style="color: red;">Failed to get stats: ${data.error || 'Unknown error'}</p>`;
                }
            } catch (error) {
                document.getElementById('stats-result').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
