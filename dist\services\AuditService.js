"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditService = void 0;
const logger_1 = require("../utils/logger");
class AuditService {
    constructor(db) {
        this.db = db;
    }
    async logChange(data) {
        try {
            const query = `
        INSERT INTO audit_trail (
          table_name, record_id, action, old_values, new_values, 
          changed_by, changed_at, ip_address, user_agent
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, $7, $8)
      `;
            await this.db.query(query, [
                data.tableName,
                data.recordId,
                data.action,
                data.oldValues ? JSON.stringify(data.oldValues) : null,
                data.newValues ? JSON.stringify(data.newValues) : null,
                data.changedBy,
                data.ipAddress,
                data.userAgent
            ]);
            logger_1.logger.info('Audit trail entry created', {
                tableName: data.tableName,
                recordId: data.recordId,
                action: data.action,
                changedBy: data.changedBy
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to create audit trail entry', { error, data });
            throw new Error('Failed to log audit trail entry');
        }
    }
    async queryAuditTrail(query) {
        try {
            const conditions = [];
            const params = [];
            let paramIndex = 1;
            if (query.tableName) {
                conditions.push(`table_name = $${paramIndex++}`);
                params.push(query.tableName);
            }
            if (query.recordId) {
                conditions.push(`record_id = $${paramIndex++}`);
                params.push(query.recordId);
            }
            if (query.action) {
                conditions.push(`action = $${paramIndex++}`);
                params.push(query.action);
            }
            if (query.changedBy) {
                conditions.push(`changed_by = $${paramIndex++}`);
                params.push(query.changedBy);
            }
            if (query.dateFrom) {
                conditions.push(`changed_at >= $${paramIndex++}`);
                params.push(query.dateFrom);
            }
            if (query.dateTo) {
                conditions.push(`changed_at <= $${paramIndex++}`);
                params.push(query.dateTo);
            }
            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
            const limit = query.limit || 50;
            const offset = query.offset || 0;
            const countQuery = `
        SELECT COUNT(*) as total
        FROM audit_trail
        ${whereClause}
      `;
            const countResult = await this.db.query(countQuery, params);
            const total = parseInt(countResult.rows[0].total);
            const entriesQuery = `
        SELECT 
          id, table_name, record_id, action, old_values, new_values,
          changed_by, changed_at, ip_address, user_agent
        FROM audit_trail
        ${whereClause}
        ORDER BY changed_at DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;
            params.push(limit, offset);
            const entriesResult = await this.db.query(entriesQuery, params);
            const entries = entriesResult.rows.map(row => ({
                id: row.id,
                tableName: row.table_name,
                recordId: row.record_id,
                action: row.action,
                oldValues: row.old_values ? JSON.parse(row.old_values) : undefined,
                newValues: row.new_values ? JSON.parse(row.new_values) : undefined,
                changedBy: row.changed_by,
                changedAt: new Date(row.changed_at),
                ipAddress: row.ip_address,
                userAgent: row.user_agent
            }));
            return { entries, total };
        }
        catch (error) {
            logger_1.logger.error('Failed to query audit trail', { error, query });
            throw new Error('Failed to query audit trail');
        }
    }
    async getAuditSummary(tableName, recordId) {
        try {
            const conditions = [];
            const params = [];
            let paramIndex = 1;
            if (tableName) {
                conditions.push(`table_name = $${paramIndex++}`);
                params.push(tableName);
            }
            if (recordId) {
                conditions.push(`record_id = $${paramIndex++}`);
                params.push(recordId);
            }
            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
            const totalQuery = `
        SELECT COUNT(*) as total
        FROM audit_trail
        ${whereClause}
      `;
            const totalResult = await this.db.query(totalQuery, params);
            const totalChanges = parseInt(totalResult.rows[0].total);
            const actionQuery = `
        SELECT action, COUNT(*) as count
        FROM audit_trail
        ${whereClause}
        GROUP BY action
      `;
            const actionResult = await this.db.query(actionQuery, params);
            const changesByAction = {};
            actionResult.rows.forEach(row => {
                changesByAction[row.action] = parseInt(row.count);
            });
            const userQuery = `
        SELECT changed_by, COUNT(*) as count
        FROM audit_trail
        ${whereClause}
        AND changed_by IS NOT NULL
        GROUP BY changed_by
        ORDER BY count DESC
        LIMIT 10
      `;
            const userResult = await this.db.query(userQuery, params);
            const changesByUser = {};
            userResult.rows.forEach(row => {
                changesByUser[row.changed_by] = parseInt(row.count);
            });
            const recentQuery = `
        SELECT 
          id, table_name, record_id, action, old_values, new_values,
          changed_by, changed_at, ip_address, user_agent
        FROM audit_trail
        ${whereClause}
        ORDER BY changed_at DESC
        LIMIT 10
      `;
            const recentResult = await this.db.query(recentQuery, params);
            const recentChanges = recentResult.rows.map(row => ({
                id: row.id,
                tableName: row.table_name,
                recordId: row.record_id,
                action: row.action,
                oldValues: row.old_values ? JSON.parse(row.old_values) : undefined,
                newValues: row.new_values ? JSON.parse(row.new_values) : undefined,
                changedBy: row.changed_by,
                changedAt: new Date(row.changed_at),
                ipAddress: row.ip_address,
                userAgent: row.user_agent
            }));
            return {
                totalChanges,
                changesByAction,
                changesByUser,
                recentChanges
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get audit summary', { error, tableName, recordId });
            throw new Error('Failed to get audit summary');
        }
    }
    async getChangeHistory(entityType, entityId) {
        try {
            const tableName = this.getTableNameFromEntityType(entityType);
            const query = `
        SELECT 
          a.id, a.table_name, a.record_id, a.action, a.old_values, a.new_values,
          a.changed_by, a.changed_at, a.ip_address,
          u.username as changed_by_name,
          CASE 
            WHEN a.table_name = 'projects' THEN p.name
            WHEN a.table_name = 'files' THEN f.name
            WHEN a.table_name = 'versions' THEN CONCAT('Version ', v.version)
            ELSE 'Unknown'
          END as entity_name
        FROM audit_trail a
        LEFT JOIN users u ON a.changed_by = u.id
        LEFT JOIN projects p ON a.table_name = 'projects' AND a.record_id = p.id
        LEFT JOIN files f ON a.table_name = 'files' AND a.record_id = f.id
        LEFT JOIN versions v ON a.table_name = 'versions' AND a.record_id = v.id
        WHERE a.table_name = $1 AND a.record_id = $2
        ORDER BY a.changed_at DESC
      `;
            const result = await this.db.query(query, [tableName, entityId]);
            return result.rows.map(row => ({
                id: row.id,
                entityType,
                entityId: row.record_id,
                entityName: row.entity_name || 'Unknown',
                action: this.formatAction(row.action, row.old_values, row.new_values),
                description: this.generateChangeDescription(row.action, row.old_values, row.new_values, entityType),
                changedBy: row.changed_by,
                changedByName: row.changed_by_name,
                changedAt: new Date(row.changed_at),
                oldValues: row.old_values ? JSON.parse(row.old_values) : undefined,
                newValues: row.new_values ? JSON.parse(row.new_values) : undefined,
                ipAddress: row.ip_address
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get change history', { error, entityType, entityId });
            throw new Error('Failed to get change history');
        }
    }
    async getComplianceReport(dateFrom, dateTo) {
        try {
            const totalQuery = `
        SELECT COUNT(*) as total
        FROM audit_trail
        WHERE changed_at >= $1 AND changed_at <= $2
      `;
            const totalResult = await this.db.query(totalQuery, [dateFrom, dateTo]);
            const totalChanges = parseInt(totalResult.rows[0].total);
            const tableQuery = `
        SELECT table_name, COUNT(*) as count
        FROM audit_trail
        WHERE changed_at >= $1 AND changed_at <= $2
        GROUP BY table_name
        ORDER BY count DESC
      `;
            const tableResult = await this.db.query(tableQuery, [dateFrom, dateTo]);
            const changesByTable = {};
            tableResult.rows.forEach(row => {
                changesByTable[row.table_name] = parseInt(row.count);
            });
            const userQuery = `
        SELECT a.changed_by, u.username, COUNT(*) as count
        FROM audit_trail a
        LEFT JOIN users u ON a.changed_by = u.id
        WHERE a.changed_at >= $1 AND a.changed_at <= $2
        AND a.changed_by IS NOT NULL
        GROUP BY a.changed_by, u.username
        ORDER BY count DESC
      `;
            const userResult = await this.db.query(userQuery, [dateFrom, dateTo]);
            const changesByUser = {};
            userResult.rows.forEach(row => {
                changesByUser[row.changed_by] = {
                    count: parseInt(row.count),
                    username: row.username || 'Unknown User'
                };
            });
            const criticalQuery = `
        SELECT 
          id, table_name, record_id, action, old_values, new_values,
          changed_by, changed_at, ip_address, user_agent
        FROM audit_trail
        WHERE changed_at >= $1 AND changed_at <= $2
        AND (
          action = 'DELETE' 
          OR (table_name = 'versions' AND new_values::jsonb->>'is_released' = 'true')
        )
        ORDER BY changed_at DESC
      `;
            const criticalResult = await this.db.query(criticalQuery, [dateFrom, dateTo]);
            const criticalChanges = criticalResult.rows.map(row => ({
                id: row.id,
                tableName: row.table_name,
                recordId: row.record_id,
                action: row.action,
                oldValues: row.old_values ? JSON.parse(row.old_values) : undefined,
                newValues: row.new_values ? JSON.parse(row.new_values) : undefined,
                changedBy: row.changed_by,
                changedAt: new Date(row.changed_at),
                ipAddress: row.ip_address,
                userAgent: row.user_agent
            }));
            return {
                totalChanges,
                changesByTable,
                changesByUser,
                criticalChanges
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to generate compliance report', { error, dateFrom, dateTo });
            throw new Error('Failed to generate compliance report');
        }
    }
    getTableNameFromEntityType(entityType) {
        switch (entityType) {
            case 'project':
                return 'projects';
            case 'file':
                return 'files';
            case 'version':
                return 'versions';
            default:
                throw new Error(`Unknown entity type: ${entityType}`);
        }
    }
    formatAction(action, oldValues, newValues) {
        switch (action) {
            case 'INSERT':
                return 'Created';
            case 'UPDATE':
                return 'Modified';
            case 'DELETE':
                return 'Deleted';
            default:
                return action;
        }
    }
    generateChangeDescription(action, oldValues, newValues, entityType) {
        try {
            const oldData = oldValues ? JSON.parse(oldValues) : null;
            const newData = newValues ? JSON.parse(newValues) : null;
            switch (action) {
                case 'INSERT':
                    return `${entityType || 'Record'} was created`;
                case 'DELETE':
                    return `${entityType || 'Record'} was deleted`;
                case 'UPDATE':
                    if (!oldData || !newData) {
                        return `${entityType || 'Record'} was modified`;
                    }
                    const changes = [];
                    const keys = new Set([...Object.keys(oldData), ...Object.keys(newData)]);
                    for (const key of keys) {
                        if (oldData[key] !== newData[key]) {
                            if (key === 'updated_at' || key === 'last_accessed_at') {
                                continue;
                            }
                            const oldValue = oldData[key] || 'null';
                            const newValue = newData[key] || 'null';
                            changes.push(`${key}: "${oldValue}" → "${newValue}"`);
                        }
                    }
                    return changes.length > 0
                        ? `Modified: ${changes.join(', ')}`
                        : `${entityType || 'Record'} was modified`;
                default:
                    return `${entityType || 'Record'} was ${action.toLowerCase()}`;
            }
        }
        catch (error) {
            return `${entityType || 'Record'} was ${action.toLowerCase()}`;
        }
    }
}
exports.AuditService = AuditService;
//# sourceMappingURL=AuditService.js.map