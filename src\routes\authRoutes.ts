import { Router, Request, Response } from 'express';
import { Pool } from 'pg';
import { AuthenticationService } from '../services/AuthenticationService';
import { AuthorizationService } from '../services/AuthorizationService';
import { SessionService } from '../services/SessionService';
import { auth } from '../middleware/auth';

export function createAuthRoutes(db: Pool): Router {
  const router = Router();
  
  // Initialize services
  const jwtSecret = process.env['JWT_SECRET'] || 'default-secret-key';
  const authService = new AuthenticationService(db, jwtSecret);
  // Note: AuthorizationService and SessionService need to be implemented
  // const authzService = new AuthorizationService(db);
  // const sessionService = new SessionService(db);

  /**
   * POST /api/auth/login
   * Authenticate user and return JWT token
   */
  router.post('/login', async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }

      const result = await authService.authenticate(username, password);
      
      // Create session (TODO: Implement SessionService)
      // const sessionData = {
      //   userId: result.user.id,
      //   token: result.token,
      //   ipAddress: req.ip,
      //   userAgent: req.get('User-Agent') || 'Unknown'
      // };
      //
      // await sessionService.createSession(sessionData);

      res.json({
        success: true,
        data: {
          user: {
            id: result.user.id,
            username: result.user.username,
            email: result.user.email,
            role: result.user.role,
            createdAt: result.user.createdAt?.toISOString() || new Date().toISOString(),
            updatedAt: result.user.updatedAt?.toISOString() || new Date().toISOString()
          },
          token: {
            token: result.token,
            refreshToken: result.refreshToken || result.token, // Use same token if no refresh token
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
          }
        }
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(401).json({
        success: false,
        error: 'Invalid username or password'
      });
    }
  });

  /**
   * POST /api/auth/logout
   * Logout user and invalidate token
   */
  router.post('/logout', auth, async (req: Request, res: Response) => {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');

      // TODO: Implement SessionService
      // if (token) {
      //   await sessionService.invalidateSession(token);
      // }

      res.json({
        message: 'Logged out successfully'
      });

    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        error: {
          code: 'LOGOUT_FAILED',
          message: 'Failed to logout',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * POST /api/auth/refresh
   * Refresh JWT token
   */
  router.post('/refresh', async (req: Request, res: Response) => {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          error: {
            code: 'MISSING_TOKEN',
            message: 'Token is required',
            timestamp: new Date().toISOString()
          }
        });
      }

      const newToken = await authService.refreshToken(token);

      res.json({
        success: true,
        data: {
          token: newToken,
          refreshToken: newToken, // Use same token as refresh token for simplicity
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        }
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        error: {
          code: 'TOKEN_REFRESH_FAILED',
          message: 'Failed to refresh token',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * GET /api/auth/me
   * Get current user information
   */
  router.get('/me', auth, async (req: Request, res: Response) => {
    try {
      const user = (req as any).user;

      res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          createdAt: user.createdAt?.toISOString() || new Date().toISOString(),
          updatedAt: user.updatedAt?.toISOString() || new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Get user info error:', error);
      res.status(500).json({
        error: {
          code: 'USER_INFO_FAILED',
          message: 'Failed to get user information',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * POST /api/auth/validate
   * Validate JWT token
   */
  router.post('/validate', async (req: Request, res: Response) => {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          error: {
            code: 'MISSING_TOKEN',
            message: 'Token is required',
            timestamp: new Date().toISOString()
          }
        });
      }

      const user = await authService.validateToken(token);

      res.json({
        valid: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        }
      });

    } catch (error) {
      res.status(401).json({
        valid: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Token is invalid or expired',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * GET /api/auth/sessions
   * Get active sessions for current user
   */
  router.get('/sessions', auth, async (req: Request, res: Response) => {
    try {
      const user = (req as any).user;
      // TODO: Implement SessionService
      // const sessions = await sessionService.getUserSessions(user.id);

      res.json({
        sessions: [] // TODO: Return actual sessions when SessionService is implemented
        // sessions: sessions.map(session => ({
        //   id: session.id,
        //   createdAt: session.createdAt,
        //   lastAccessedAt: session.lastAccessedAt,
        //   ipAddress: session.ipAddress,
        //   userAgent: session.userAgent,
        //   isActive: session.isActive
        // }))
      });

    } catch (error) {
      console.error('Get sessions error:', error);
      res.status(500).json({
        error: {
          code: 'SESSIONS_FAILED',
          message: 'Failed to get sessions',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * DELETE /api/auth/sessions/:sessionId
   * Revoke a specific session
   */
  router.delete('/sessions/:sessionId', auth, async (req: Request, res: Response) => {
    try {
      const { sessionId } = req.params;
      const user = (req as any).user;

      // TODO: Implement SessionService
      // await sessionService.revokeSession(sessionId, user.id);

      res.json({
        message: 'Session revoked successfully'
      });

    } catch (error) {
      console.error('Revoke session error:', error);
      res.status(500).json({
        error: {
          code: 'REVOKE_SESSION_FAILED',
          message: 'Failed to revoke session',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  return router;
}
