"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAuthRoutes = createAuthRoutes;
const express_1 = require("express");
const AuthenticationService_1 = require("../services/AuthenticationService");
const auth_1 = require("../middleware/auth");
function createAuthRoutes(db) {
    const router = (0, express_1.Router)();
    const jwtSecret = process.env['JWT_SECRET'] || 'default-secret-key';
    const authService = new AuthenticationService_1.AuthenticationService(db, jwtSecret);
    router.post('/login', async (req, res) => {
        try {
            const { username, password } = req.body;
            if (!username || !password) {
                return res.status(400).json({
                    success: false,
                    error: 'Username and password are required'
                });
            }
            const result = await authService.authenticate(username, password);
            res.json({
                success: true,
                data: {
                    user: {
                        id: result.user.id,
                        username: result.user.username,
                        email: result.user.email,
                        role: result.user.role,
                        createdAt: result.user.createdAt?.toISOString() || new Date().toISOString(),
                        updatedAt: result.user.updatedAt?.toISOString() || new Date().toISOString()
                    },
                    token: {
                        token: result.token,
                        refreshToken: result.refreshToken || result.token,
                        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                    }
                }
            });
        }
        catch (error) {
            console.error('Login error:', error);
            res.status(401).json({
                success: false,
                error: 'Invalid username or password'
            });
        }
    });
    router.post('/logout', auth_1.auth, async (req, res) => {
        try {
            const token = req.headers.authorization?.replace('Bearer ', '');
            res.json({
                message: 'Logged out successfully'
            });
        }
        catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                error: {
                    code: 'LOGOUT_FAILED',
                    message: 'Failed to logout',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/refresh', async (req, res) => {
        try {
            const { token } = req.body;
            if (!token) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_TOKEN',
                        message: 'Token is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const newToken = await authService.refreshToken(token);
            res.json({
                success: true,
                data: {
                    token: newToken,
                    refreshToken: newToken,
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                }
            });
        }
        catch (error) {
            console.error('Token refresh error:', error);
            res.status(401).json({
                error: {
                    code: 'TOKEN_REFRESH_FAILED',
                    message: 'Failed to refresh token',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/me', auth_1.auth, async (req, res) => {
        try {
            const user = req.user;
            res.json({
                success: true,
                data: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role,
                    createdAt: user.createdAt?.toISOString() || new Date().toISOString(),
                    updatedAt: user.updatedAt?.toISOString() || new Date().toISOString()
                }
            });
        }
        catch (error) {
            console.error('Get user info error:', error);
            res.status(500).json({
                error: {
                    code: 'USER_INFO_FAILED',
                    message: 'Failed to get user information',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/validate', async (req, res) => {
        try {
            const { token } = req.body;
            if (!token) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_TOKEN',
                        message: 'Token is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const user = await authService.validateToken(token);
            res.json({
                valid: true,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role
                }
            });
        }
        catch (error) {
            res.status(401).json({
                valid: false,
                error: {
                    code: 'INVALID_TOKEN',
                    message: 'Token is invalid or expired',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/sessions', auth_1.auth, async (req, res) => {
        try {
            const user = req.user;
            res.json({
                sessions: []
            });
        }
        catch (error) {
            console.error('Get sessions error:', error);
            res.status(500).json({
                error: {
                    code: 'SESSIONS_FAILED',
                    message: 'Failed to get sessions',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.delete('/sessions/:sessionId', auth_1.auth, async (req, res) => {
        try {
            const { sessionId } = req.params;
            const user = req.user;
            res.json({
                message: 'Session revoked successfully'
            });
        }
        catch (error) {
            console.error('Revoke session error:', error);
            res.status(500).json({
                error: {
                    code: 'REVOKE_SESSION_FAILED',
                    message: 'Failed to revoke session',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    return router;
}
//# sourceMappingURL=authRoutes.js.map