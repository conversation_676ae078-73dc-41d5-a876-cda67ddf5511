<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication</title>
</head>
<body>
    <h1>Test Authentication</h1>
    <div id="result"></div>
    
    <script>
        async function testAuth() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Test login
                const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const loginData = await loginResponse.json();
                console.log('Login response:', loginData);
                
                if (loginData.success) {
                    resultDiv.innerHTML += '<p>✅ Login successful!</p>';
                    
                    // Test authenticated request
                    const projectsResponse = await fetch('http://localhost:3001/api/projects', {
                        headers: {
                            'Authorization': `Bearer ${loginData.data.token.token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const projectsData = await projectsResponse.json();
                    console.log('Projects response:', projectsData);
                    
                    if (projectsResponse.ok) {
                        resultDiv.innerHTML += '<p>✅ Authenticated API call successful!</p>';
                        resultDiv.innerHTML += `<p>Projects: ${JSON.stringify(projectsData, null, 2)}</p>`;
                    } else {
                        resultDiv.innerHTML += '<p>❌ Authenticated API call failed</p>';
                    }
                } else {
                    resultDiv.innerHTML += '<p>❌ Login failed</p>';
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testAuth();
    </script>
</body>
</html>