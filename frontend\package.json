{"name": "e3-pdm-frontend", "version": "1.0.0", "description": "React frontend for E3 PDM System", "private": true, "dependencies": {"axios": "^1.6.0", "date-fns": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "babel-jest": "^30.0.5", "identity-obj-proxy": "^3.0.0", "jest-environment-jsdom": "^30.0.5", "react-scripts": "^5.0.1", "ts-jest": "^29.4.0", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}