import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';
import { User } from '../types';
import { AuthenticationService as IAuthenticationService } from '../types/services';

export class AuthenticationService implements IAuthenticationService {
  private db: Pool;
  private jwtSecret: string;
  private jwtExpiresIn: string;
  private refreshTokens: Set<string> = new Set();

  constructor(db: Pool, jwtSecret: string, jwtExpiresIn: string = '1h') {
    this.db = db;
    this.jwtSecret = jwtSecret;
    this.jwtExpiresIn = jwtExpiresIn;
  }

  async authenticate(username: string, password: string): Promise<{ user: User; token: string }> {
    const client = await this.db.connect();
    
    try {
      // Find user by username or email
      const userQuery = `
        SELECT id, username, email, password_hash, role, created_at, last_login, is_active
        FROM users 
        WHERE (username = $1 OR email = $1) AND is_active = true
      `;
      
      const result = await client.query(userQuery, [username]);
      
      if (result.rows.length === 0) {
        throw new Error('Invalid credentials');
      }

      const userData = result.rows[0];
      
      // Verify password
      const isValidPassword = await bcrypt.compare(password, userData.password_hash);
      if (!isValidPassword) {
        throw new Error('Invalid credentials');
      }

      // Update last login
      await client.query(
        'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
        [userData.id]
      );

      // Create user object
      const user: User = {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        role: userData.role,
        createdAt: userData.created_at,
        lastLogin: new Date()
      };

      // Generate JWT token
      const token = this.generateToken(user);
      this.refreshTokens.add(token);

      return { user, token };
    } finally {
      client.release();
    }
  }

  async validateToken(token: string): Promise<User> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      // Note: In production, you should check if token is revoked in database
      // For now, we'll just validate the JWT signature and expiration

      const client = await this.db.connect();
      
      try {
        const userQuery = `
          SELECT id, username, email, role, created_at, last_login
          FROM users 
          WHERE id = $1 AND is_active = true
        `;
        
        const result = await client.query(userQuery, [decoded.userId]);
        
        if (result.rows.length === 0) {
          throw new Error('User not found or inactive');
        }

        const userData = result.rows[0];
        
        return {
          id: userData.id,
          username: userData.username,
          email: userData.email,
          role: userData.role,
          createdAt: userData.created_at,
          lastLogin: userData.last_login
        };
      } finally {
        client.release();
      }
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  async refreshToken(token: string): Promise<string> {
    try {
      // Validate current token
      const user = await this.validateToken(token);
      
      // Remove old token
      this.refreshTokens.delete(token);
      
      // Generate new token
      const newToken = this.generateToken(user);
      this.refreshTokens.add(newToken);
      
      return newToken;
    } catch (error) {
      throw new Error('Unable to refresh token');
    }
  }

  async logout(token: string): Promise<void> {
    this.refreshTokens.delete(token);
  }

  async createUser(username: string, email: string, password: string, role: 'admin' | 'user' = 'user'): Promise<User> {
    const client = await this.db.connect();
    
    try {
      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);
      
      // Insert user
      const insertQuery = `
        INSERT INTO users (username, email, password_hash, role)
        VALUES ($1, $2, $3, $4)
        RETURNING id, username, email, role, created_at
      `;
      
      const result = await client.query(insertQuery, [username, email, passwordHash, role]);
      const userData = result.rows[0];
      
      return {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        role: userData.role,
        createdAt: userData.created_at
      };
    } catch (error: any) {
      if (error.code === '23505') { // Unique constraint violation
        if (error.constraint?.includes('username')) {
          throw new Error('Username already exists');
        } else if (error.constraint?.includes('email')) {
          throw new Error('Email already exists');
        }
      }
      throw error;
    } finally {
      client.release();
    }
  }

  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    const client = await this.db.connect();
    
    try {
      // Get current password hash
      const userQuery = 'SELECT password_hash FROM users WHERE id = $1 AND is_active = true';
      const result = await client.query(userQuery, [userId]);
      
      if (result.rows.length === 0) {
        throw new Error('User not found');
      }
      
      // Verify current password
      const isValidPassword = await bcrypt.compare(currentPassword, result.rows[0].password_hash);
      if (!isValidPassword) {
        throw new Error('Current password is incorrect');
      }
      
      // Hash new password
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
      
      // Update password
      await client.query(
        'UPDATE users SET password_hash = $1 WHERE id = $2',
        [newPasswordHash, userId]
      );
    } finally {
      client.release();
    }
  }

  private generateToken(user: User): string {
    const payload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };
    
    return jwt.sign(payload, this.jwtSecret, { 
      expiresIn: this.jwtExpiresIn,
      issuer: 'e3-pdm-system',
      audience: 'e3-pdm-users'
    } as jwt.SignOptions);
  }
}