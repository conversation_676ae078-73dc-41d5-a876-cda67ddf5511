import { Pool } from 'pg';
import { AuditTrailEntry, AuditTrailQuery, AuditTrailSummary, ChangeHistoryEntry } from '../types';
export declare class AuditService {
    private db;
    constructor(db: Pool);
    logChange(data: {
        tableName: string;
        recordId: string;
        action: 'INSERT' | 'UPDATE' | 'DELETE';
        oldValues?: Record<string, any>;
        newValues?: Record<string, any>;
        changedBy?: string;
        ipAddress?: string;
        userAgent?: string;
    }): Promise<void>;
    queryAuditTrail(query: AuditTrailQuery): Promise<{
        entries: AuditTrailEntry[];
        total: number;
    }>;
    getAuditSummary(tableName?: string, recordId?: string): Promise<AuditTrailSummary>;
    getChangeHistory(entityType: 'project' | 'file' | 'version', entityId: string): Promise<ChangeHistoryEntry[]>;
    getComplianceReport(dateFrom: Date, dateTo: Date): Promise<{
        totalChanges: number;
        changesByTable: Record<string, number>;
        changesByUser: Record<string, {
            count: number;
            username: string;
        }>;
        criticalChanges: AuditTrailEntry[];
    }>;
    private getTableNameFromEntityType;
    private formatAction;
    private generateChangeDescription;
}
//# sourceMappingURL=AuditService.d.ts.map